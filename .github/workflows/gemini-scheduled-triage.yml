name: '📋 Gemini Scheduled Issue Triage'

on:
  schedule:
    - cron: '0 * * * *' # Runs every hour
  pull_request:
    branches:
      - 'main'
      - 'release/**/*'
    paths:
      - '.github/workflows/gemini-scheduled-triage.yml'
  push:
    branches:
      - 'main'
      - 'release/**/*'
    paths:
      - '.github/workflows/gemini-scheduled-triage.yml'
  workflow_dispatch:

concurrency:
  group: '${{ github.workflow }}'
  cancel-in-progress: true

defaults:
  run:
    shell: 'bash'

jobs:
  triage:
    runs-on: 'ubuntu-latest'
    timeout-minutes: 7
    permissions:
      contents: 'read'
      id-token: 'write'
      issues: 'read'
      pull-requests: 'read'
    outputs:
      available_labels: '${{ steps.get_labels.outputs.available_labels }}'
      triaged_issues: '${{ steps.export_triaged.outputs.triaged_issues }}'
    steps:
      - name: 'Get repository labels'
        id: 'get_labels'
        uses: 'actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea' # ratchet:actions/github-script@v7.0.1
        with:
          # NOTE: we intentionally do not use the minted token. The default
          # GITHUB_TOKEN provided by the action has enough permissions to read
          # the labels.
          script: |-
            const { data: labels } = await github.rest.issues.listLabelsForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
            });

            if (!labels || labels.length === 0) {
              core.setFailed('There are no issue labels in this repository.')
            }

            const labelNames = labels.map(label => label.name).sort();
            core.setOutput('available_labels', labelNames.join(','));
            core.info(`Found ${labelNames.length} labels: ${labelNames.join(', ')}`);
            return labelNames;

      - name: 'Find untriaged issues'
        id: 'find_issues'
        env:
          GITHUB_REPOSITORY: '${{ github.repository }}'
          GITHUB_TOKEN: '${{ secrets.GITHUB_TOKEN || github.token }}'
        run: |-
          echo '🔍 Finding unlabeled issues and issues marked for triage...'
          ISSUES="$(gh issue list \
            --state 'open' \
            --search 'no:label,label:"gemini:triage"' \
            --json 'number,title,body' \
            --limit 50)"
          echo "Found $(echo "$ISSUES" | jq '. | length') issues to triage."
          echo "issues_to_triage=$ISSUES" >> "$GITHUB_OUTPUT"

      - name: 'Sanitize GITHUB_ENV (fix malformed env writes)'
        if: always()
        shell: bash
        run: |
          set -euo pipefail
          ENV_FILE="${GITHUB_ENV}"
          if [ -f "$ENV_FILE" ] && grep -qE "^[[:space:]]*[{\[]" "$ENV_FILE" 2>/dev/null; then
            JSON_BLOCK=$(awk 'BEGIN{found=0} /^[[:space:]]*\[/{found=1} found{print} /^\]/{print; exit}' "$ENV_FILE" || true)
            if [ -z "$JSON_BLOCK" ]; then
              JSON_BLOCK=$(awk 'BEGIN{found=0} /^[[:space:]]*\{/{found=1} found{print} /^\}/{print; exit}' "$ENV_FILE" || true)
            fi
            if [ -n "$JSON_BLOCK" ]; then
              COMPACT_JSON=$(echo "$JSON_BLOCK" | jq -c '.' 2>/dev/null || true)
              if [ -n "$COMPACT_JSON" ]; then
                VAR_NAME="TRIAGED_ISSUES"
                TMP=$(mktemp)
                awk '/^[[:space:]]*\[/{flag=1} flag && /^\]/{flag=0; next} !flag{print}' "$ENV_FILE" > "$TMP" || true
                awk '/^[[:space:]]*\{/{flag=1} flag && /^\}/{flag=0; next} !flag{print}' "$TMP" > "$TMP.tmp" || true
                mv "$TMP.tmp" "$TMP" || true
                printf '%s=%s\n' "$VAR_NAME" "$COMPACT_JSON" >> "$TMP"
                mv "$TMP" "$ENV_FILE"
                echo "Sanitized $ENV_FILE and set $VAR_NAME"

                  # Validate compact JSON conforms to expected schema
                  IS_VALID=$(echo "$COMPACT_JSON" | jq -e 'type=="array" and all(.[]; (.issue_number|type=="number") and (.labels_to_set|type=="array") and (.explanation|type=="string"))' 2>/dev/null || true)
                  if [ "$IS_VALID" != "true" ]; then
                    echo "Sanitized JSON did not conform to expected schema." >&2
                    echo "Sanitized JSON (truncated): ${COMPACT_JSON:0:2000}" >&2
                    TMPF=$(mktemp)
                    echo "$COMPACT_JSON" > "$TMPF"
                    echo "Saved full sanitized JSON to: $TMPF" >&2
                    echo "Suggestion: ensure the model outputs strictly the required JSON array." >&2
                    exit 1
                  fi

                  # Export compact triaged issues directly for downstream steps
                  echo "triaged_issues=$COMPACT_JSON" >> "$GITHUB_OUTPUT"
              fi
            fi
          fi

      - name: 'Export triaged issues for job outputs'
        id: export_triaged
        run: |
          set -euo pipefail
          if [ -f "$GITHUB_ENV" ]; then
            # shellcheck disable=SC1090
            source "$GITHUB_ENV" || true
          fi

          # TRIAGED_ISSUES should have been set by the sanitize step
          echo "triaged_issues=${TRIAGED_ISSUES:-}" >> "$GITHUB_OUTPUT"
  label:
    runs-on: 'ubuntu-latest'
    needs:
      - 'triage'
    if: |-
      needs.triage.outputs.available_labels != '' &&
      needs.triage.outputs.available_labels != '[]' &&
      needs.triage.outputs.triaged_issues != '' &&
      needs.triage.outputs.triaged_issues != '[]'
    permissions:
      contents: 'read'
      issues: 'write'
      pull-requests: 'write'
    steps:
      - name: 'Mint identity token'
        id: 'mint_identity_token'
        if: |-
          ${{ vars.APP_ID }}
        uses: 'actions/create-github-app-token@a8d616148505b5069dccd32f177bb87d7f39123b' # ratchet:actions/create-github-app-token@v2
        with:
          app-id: '${{ vars.APP_ID }}'
          private-key: '${{ secrets.APP_PRIVATE_KEY }}'
          permission-contents: 'read'
          permission-issues: 'write'
          permission-pull-requests: 'write'

      - name: 'Apply labels'
        env:
          AVAILABLE_LABELS: '${{ needs.triage.outputs.available_labels }}'
          TRIAGED_ISSUES: '${{ needs.triage.outputs.triaged_issues }}'
        uses: 'actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea' # ratchet:actions/github-script@v7.0.1
        with:
          # Use the provided token so that the "gemini-cli" is the actor in the
          # log for what changed the labels.
          github-token: '${{ steps.mint_identity_token.outputs.token || secrets.GITHUB_TOKEN || github.token }}'
          script: |-
            // Parse the available labels
            const availableLabels = (process.env.AVAILABLE_LABELS || '').split(',')
              .map((label) => label.trim())
              .sort()

            // Parse out the triaged issues
            const triagedIssues = (JSON.parse(process.env.TRIAGED_ISSUES || '{}'))
              .sort((a, b) => a.issue_number - b.issue_number)

            core.debug(`Triaged issues: ${JSON.stringify(triagedIssues)}`);

            // Iterate over each label
            for (const issue of triagedIssues) {
              if (!issue) {
                core.debug(`Skipping empty issue: ${JSON.stringify(issue)}`);
                continue;
              }

              const issueNumber = issue.issue_number;
              if (!issueNumber) {
                core.debug(`Skipping issue with no data: ${JSON.stringify(issue)}`);
                continue;
              }

              // Extract and reject invalid labels - we do this just in case
              // someone was able to prompt inject malicious labels.
              let labelsToSet = (issue.labels_to_set || [])
                .map((label) => label.trim())
                .filter((label) => availableLabels.includes(label))
                .sort()

              core.debug(`Identified labels to set: ${JSON.stringify(labelsToSet)}`);

              if (labelsToSet.length === 0) {
                core.info(`Skipping issue #${issueNumber} - no labels to set.`)
                continue;
              }

              core.debug(`Setting labels on issue #${issueNumber} to ${labelsToSet.join(', ')} (${issue.explanation || 'no explanation'})`)

              await github.rest.issues.addLabels({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issueNumber,
                labels: labelsToSet,
              });
            }
