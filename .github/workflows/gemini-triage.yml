name: '🔀 Gemini Triage'

on:
  workflow_call:
    inputs:
      issue_number:
        type: number
        description: 'Issue or PR number to triage'
        required: false
      issue_title:
        type: string
        description: 'Title to analyze (fallback to event payloads)'
        required: false
      issue_body:
        type: string
        description: 'Body to analyze (fallback to event payloads)'
        required: false
      additional_context:
        type: 'string'
        description: 'Any additional context from the request'
        required: false

concurrency:
  group: '${{ github.workflow }}-triage-${{ github.event_name }}-${{ github.event.pull_request.number || github.event.issue.number }}'
  cancel-in-progress: true

defaults:
  run:
    shell: 'bash'

jobs:
  triage:
    runs-on: 'ubuntu-latest'
    timeout-minutes: 7
    env:
      GEMINI_SAFE_ENV: '${{ runner.temp }}/gemini_env'
    outputs:
      available_labels: '${{ steps.get_labels.outputs.available_labels }}'
      selected_labels: '${{ steps.capture_selected.outputs.selected_labels }}'
    permissions:
      contents: 'read'
      id-token: 'write'
      issues: 'read'
      pull-requests: 'read'
    steps:
      - name: 'Get repository labels'
        id: 'get_labels'
        uses: 'actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea' # ratchet:actions/github-script@v7.0.1
        with:
          # NOTE: we intentionally do not use the given token. The default
          # GITHUB_TOKEN provided by the action has enough permissions to read
          # the labels.
          script: |-
            const { data: labels } = await github.rest.issues.listLabelsForRepo({
              owner: context.repo.owner,
              repo: context.repo.repo,
            });

            if (!labels || labels.length === 0) {
              core.setFailed('There are no issue labels in this repository.')
            }

            const labelNames = labels.map(label => label.name).sort();
            core.setOutput('available_labels', labelNames.join(','));
            core.info(`Found ${labelNames.length} labels: ${labelNames.join(', ')}`);
            return labelNames;

      - name: 'Run Gemini issue analysis (npx)'
        id: 'gemini_analysis'
        if: |-
          ${{ steps.get_labels.outputs.available_labels != '' }}
        shell: bash
        env:
          GITHUB_TOKEN: '' # Do NOT pass any auth tokens here since this runs on untrusted inputs
          ISSUE_TITLE: '${{ inputs.issue_title || github.event.issue.title || github.event.pull_request.title || '''' }}'
          ISSUE_BODY: '${{ inputs.issue_body || github.event.issue.body || github.event.pull_request.body || '''' }}'
          AVAILABLE_LABELS: '${{ steps.get_labels.outputs.available_labels }}'
          GEMINI_API_KEY: '${{ secrets.GEMINI_API_KEY }}'
          GOOGLE_API_KEY: '${{ secrets.GOOGLE_API_KEY }}'
        run: |
          set -euo pipefail
          PROMPT_FILE=$(mktemp)
          cat > "$PROMPT_FILE" <<'PROMPT'
          ## Role

          You are an issue triage assistant. Analyze the current GitHub issue and identify the most appropriate existing labels. Use the available tools to gather information; do not ask for information to be provided.

          ## Guidelines

          - Retrieve the value for environment variables using the "echo" shell command.
          - Environment variables are specified in the format "${VARIABLE}" (with quotes and braces).
          - Only use labels that are from the list of available labels.
          - You can choose multiple labels to apply.

          ## Steps

          1. Retrieve the available labels from the environment variable: "${AVAILABLE_LABELS}".

          2. Retrieve the issue title from the environment variable: "${ISSUE_TITLE}".

          3. Retrieve the issue body from the environment variable: "${ISSUE_BODY}".

          4. Review the issue title, issue body, and available labels.

          5. Based on the issue title and issue body, classify the issue and choose all appropriate labels from the list of available labels.

          6. Convert the list of appropriate labels into a comma-separated list (CSV). If there are no appropriate labels, use the empty string.

          PROMPT

          # Run the Gemini CLI via npx; capture stdout (expected to be JSON or CSV
          # according to the prompt). We pass API keys via env.
          RESULT=$(npx --yes @google/gemini-cli@latest --prompt-file "$PROMPT_FILE" 2>/dev/null || true)

          # If result is JSON array of strings, convert to CSV and write to GITHUB_OUTPUT
          if echo "$RESULT" | jq -e 'type=="array" and (.[0] | type=="string")' >/dev/null 2>&1; then
            # Convert to CSV
            CSV=$(echo "$RESULT" | jq -r 'join(",")')

            # Validate that each selected label exists in AVAILABLE_LABELS
            IFS=',' read -r -a AVAILABLE_ARR <<< "${AVAILABLE_LABELS}") || true
            # Build associative map of available labels
            declare -A AVAIL_MAP
            for l in ${AVAILABLE_LABELS//,/ }; do
              AVAIL_MAP["$l"]=1
            done

            INVALID_FOUND=0
            INVALID_LIST=()
            for sel in ${CSV//,/ }; do
              if [ -z "${AVAIL_MAP[$sel]}" ]; then
                INVALID_FOUND=1
                INVALID_LIST+=("$sel")
              fi
            done
            if [ "$INVALID_FOUND" -eq 1 ]; then
              echo "Gemini CLI returned labels not in AVAILABLE_LABELS." >&2
              echo "Allowed labels: ${AVAILABLE_LABELS}" >&2
              echo "Returned labels: ${CSV}" >&2
              echo "Invalid labels: ${INVALID_LIST[*]}" >&2
              echo "Suggestion: adjust the prompt or ensure the model outputs labels from the available set." >&2
              exit 1
            fi

            echo "selected_labels=$CSV" >> "$GITHUB_OUTPUT"

          elif echo "$RESULT" | jq -e 'type=="array" and (.[0] | type=="object")' >/dev/null 2>&1; then
            # Expect array of objects with issue_number (int), labels_to_set (array), explanation (string)
            VALID=$(echo "$RESULT" | jq -e 'all(.[]; (.issue_number|type=="number") and (.labels_to_set|type=="array") and (.explanation|type=="string"))' 2>/dev/null || true)
            if [ "$VALID" != "true" ]; then
              echo "Gemini output objects do not match expected schema" >&2
              echo "Result was: $RESULT" >&2
              exit 1
            fi
            # Compact JSON and write
            COMPACT=$(echo "$RESULT" | jq -c '.')
            echo "selected_labels=$COMPACT" >> "$GITHUB_OUTPUT"
          else
            echo "Unexpected Gemini CLI output format. Expected JSON array of strings or objects." >&2
            echo "Raw output: $RESULT" >&2
            exit 1
          fi

      - name: 'Capture selected labels for job outputs'
        id: capture_selected
        run: |
          set -euo pipefail
          # Load any variables that the sanitize step wrote into the GITHUB_ENV file
          if [ -f "$GITHUB_ENV" ]; then
            # shellcheck disable=SC1090
            source "$GITHUB_ENV" || true
          fi

          # Export the selected labels as a step output
          echo "selected_labels=${SELECTED_LABELS:-}" >> "$GITHUB_OUTPUT"

      - name: 'Sanitize Gemini env file and export outputs'
        if: always()
        shell: bash
        run: |
          set -euo pipefail
          # Use the safe file that the Gemini action wrote to, falling back to real GITHUB_ENV
          ENV_FILE="${GEMINI_SAFE_ENV:-${GITHUB_ENV}}"
          # If the file contains pretty-printed JSON (lines starting with spaces and { or [),
          # extract the first JSON block and rewrite it as a single-line env var.
          if [ -f "$ENV_FILE" ] && grep -qE "^[[:space:]]*[{\[]" "$ENV_FILE" 2>/dev/null; then
            # Extract the first JSON block (from first [ or { to matching closing )
            # We'll try to capture arrays and objects; prefer arrays (triaged issues)
            JSON_BLOCK=$(awk 'BEGIN{found=0} /^[[:space:]]*\[/{found=1} found{print} /^\]/{print; exit}' "$ENV_FILE" || true)
            if [ -z "$JSON_BLOCK" ]; then
              JSON_BLOCK=$(awk 'BEGIN{found=0} /^[[:space:]]*\{/{found=1} found{print} /^\}/{print; exit}' "$ENV_FILE" || true)
            fi

            if [ -n "$JSON_BLOCK" ]; then
              # Compact the JSON into a single line
              COMPACT_JSON=$(echo "$JSON_BLOCK" | jq -c '.' 2>/dev/null || true)
              if [ -n "$COMPACT_JSON" ]; then
                # Decide variable name
                VAR_NAME="SELECTED_LABELS"
                # If the compact JSON is an array of objects with issue_number, use TRIAGED_ISSUES
                if echo "$COMPACT_JSON" | jq -e '.[0] and .[0].issue_number' >/dev/null 2>&1; then
                  VAR_NAME="TRIAGED_ISSUES"
                fi

                # Remove the pretty-printed JSON block from the env file
                # Keep existing well-formed KEY= lines, and write back sanitized file
                TMP=$(mktemp)
                awk '/^[[:space:]]*\[/{flag=1} flag && /^\]/{flag=0; next} !flag{print}' "$ENV_FILE" > "$TMP" || true
                # If object block removal required (instead of array)
                awk '/^[[:space:]]*\{/{flag=1} flag && /^\}/{flag=0; next} !flag{print}' "$TMP" > "$TMP.tmp" || true
                mv "$TMP.tmp" "$TMP" || true

                printf '%s=%s\n' "$VAR_NAME" "$COMPACT_JSON" >> "$TMP"
                mv "$TMP" "$ENV_FILE"
                echo "Sanitized $ENV_FILE and set $VAR_NAME"

                # Also export a step output directly to GITHUB_OUTPUT so callers don't need to
                # rely on sourcing a potentially malformed env file. For triage: selected_labels
                if [ "$VAR_NAME" = "SELECTED_LABELS" ]; then
                  # If the compact JSON is a JSON array of strings, convert to CSV
                  if echo "$COMPACT_JSON" | jq -e 'type=="array" and (.[0] | type=="string")' >/dev/null 2>&1; then
                    CSV=$(echo "$COMPACT_JSON" | jq -r 'join(",")')
                    echo "selected_labels=$CSV" >> "$GITHUB_OUTPUT"
                  else
                    # Fallback: write compact JSON as-is
                    echo "selected_labels=$COMPACT_JSON" >> "$GITHUB_OUTPUT"
                  fi
                else
                  # Not expected in this workflow, but write an empty selected_labels
                  echo "selected_labels=" >> "$GITHUB_OUTPUT"
                fi
              fi
            fi
          fi

  label:
    runs-on: 'ubuntu-latest'
    needs:
      - 'triage'
    if: |-
      ${{ needs.triage.outputs.selected_labels != '' }}
    permissions:
      contents: 'read'
      issues: 'write'
      pull-requests: 'write'
    steps:
      - name: 'Mint identity token'
        id: 'mint_identity_token'
        if: |-
          ${{ vars.APP_ID }}
        uses: 'actions/create-github-app-token@a8d616148505b5069dccd32f177bb87d7f39123b' # ratchet:actions/create-github-app-token@v2
        with:
          app-id: '${{ vars.APP_ID }}'
          private-key: '${{ secrets.APP_PRIVATE_KEY }}'
          permission-contents: 'read'
          permission-issues: 'write'
          permission-pull-requests: 'write'

      - name: 'Apply labels'
        env:
          ISSUE_NUMBER: '${{ inputs.issue_number || github.event.issue.number || github.event.pull_request.number }}'
          AVAILABLE_LABELS: '${{ needs.triage.outputs.available_labels }}'
          SELECTED_LABELS: '${{ needs.triage.outputs.selected_labels }}'
        uses: 'actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea' # ratchet:actions/github-script@v7.0.1
        with:
          # Use the provided token so that the "gemini-cli" is the actor in the
          # log for what changed the labels.
          github-token: '${{ steps.mint_identity_token.outputs.token || secrets.GITHUB_TOKEN || github.token }}'
          script: |-
            // Parse the available labels
            const availableLabels = (process.env.AVAILABLE_LABELS || '').split(',')
              .map((label) => label.trim())
              .sort()

            // Parse the label as a CSV, reject invalid ones - we do this just
            // in case someone was able to prompt inject malicious labels.
            const selectedLabels = (process.env.SELECTED_LABELS || '').split(',')
              .map((label) => label.trim())
              .filter((label) => availableLabels.includes(label))
              .sort()

            // Set the labels
            const issueNumber = process.env.ISSUE_NUMBER;
            if (selectedLabels && selectedLabels.length > 0) {
              await github.rest.issues.addLabels({
                owner: context.repo.owner,
                repo: context.repo.repo,
                issue_number: issueNumber,
                labels: selectedLabels,
              });
              core.info(`Successfully set labels: ${selectedLabels.join(',')}`);
            } else {
              core.info(`Failed to determine labels to set. There may not be enough information in the issue or pull request.`);
            }
