# Document de Conception - Application IA Devis Rénovation

## Vue d'ensemble

L'application de devis de rénovation automatisés est conçue comme une architecture moderne en microservices avec une interface web responsive. Le système intègre des capacités d'IA avancées pour l'analyse de contenu multimédia (texte, images, plans) et génère automatiquement des devis professionnels que les artisans peuvent valider et personnaliser.

## Architecture

### Architecture Globale avec Supabase

\`\`\`mermaid
graph TB
    subgraph "Frontend"
        CW[Client Web App]
        PW[Professional Web App]
    end
    
    subgraph "Supabase Backend"
        SB[Supabase API]
        AUTH[Supabase Auth]
        DB[(PostgreSQL)]
        STORAGE[Supabase Storage]
        RT[Realtime]
    end
    
    subgraph "Custom Services"
        AIS[AI Analysis Service]
        NS[Notification Service]
        PE[Price Engine]
    end
    
    subgraph "AI/ML Layer"
        VA[Vision AI]
        NLP[Text Analysis]
    end
    
    subgraph "External APIs"
        SP[Supplier APIs]
        EA[Email Service]
    end
    
    CW --> SB
    PW --> SB
    CW --> AUTH
    PW --> AUTH
    
    SB --> DB
    SB --> STORAGE
    SB --> RT
    
    PW --> AIS
    AIS --> VA
    AIS --> NLP
    AIS --> SB
    
    PE --> SP
    PE --> SB
    
    NS --> EA
    NS --> SB
\`\`\`

### Couches Applicatives

**Couche Présentation**
- Interface client responsive (React/Next.js)
- Interface professionnel avec dashboard avancé
- Supabase Client SDK pour les APIs
- Authentification Supabase Auth avec RLS

**Couche Métier**
- Services de gestion des devis (Supabase + Edge Functions)
- Moteur d'analyse IA (Service externe + Edge Functions)
- Système de notifications (Supabase + Edge Functions)
- Gestion des templates et catalogues (Supabase)

**Couche Données**
- Base de données PostgreSQL (Supabase)
- Row Level Security (RLS) pour la sécurité
- Stockage de fichiers (Supabase Storage)
- Full-text search intégré PostgreSQL

## Composants et Interfaces

### 1. Authentification Supabase

**Responsabilités:**
- Gestion des comptes utilisateurs (clients/professionnels)
- Authentification avec Supabase Auth
- Autorisation basée sur Row Level Security (RLS)
- Intégration OAuth2 (Google, Microsoft) via Supabase

**APIs Supabase Auth:**
\`\`\`typescript
// Utilisation du SDK Supabase
supabase.auth.signUp()
supabase.auth.signIn()
supabase.auth.signOut()
supabase.auth.getUser()
supabase.auth.updateUser()
supabase.auth.resetPasswordForEmail()
\`\`\`

**Technologies:**
- Supabase Auth (JWT automatique)
- Row Level Security (RLS) PostgreSQL
- Profils utilisateurs dans table `profiles`

### 2. Service de Gestion des Devis (Quote Service)

**Responsabilités:**
- CRUD des demandes et devis
- Workflow de validation professionnel
- Génération PDF des devis
- Conversion devis → facture
- Gestion des templates

**APIs Principales:**
\`\`\`typescript
POST /quotes/requests          // Soumission client
GET /quotes/requests          // Liste pour professionnel
GET /quotes/requests/:id      // Détail d'une demande
PUT /quotes/:id/estimate      // Mise à jour estimation
POST /quotes/:id/send         // Envoi au client
POST /quotes/:id/convert      // Conversion en facture
\`\`\`

**Schéma de Base de Données Existant:**

Les tables principales sont déjà créées dans Supabase :

- **`requests`** : Demandes clients avec données JSON flexibles
- **`quotes`** : Devis générés avec statuts et totaux
- **`quote_items`** : Lignes de devis détaillées avec calculs automatiques
- **`quote_history`** : Historique des modifications pour audit
- **`files`** : Fichiers uploadés (photos, plans)
- **`profiles`** : Profils utilisateurs (clients/professionnels)
- **`templates`** : Templates de devis réutilisables
- **`products_catalog`** : Catalogue produits avec prix
- **`invoices`** : Factures générées depuis les devis
- **`notifications`** : Système de notifications email

### 3. Service d'Analyse IA (Edge Functions)

**Responsabilités:**
- Analyse de texte (NLP) pour extraction d'entités
- Analyse d'images pour reconnaissance de matériaux
- Interprétation de plans architecturaux
- Calcul automatique des quantités et coûts

**Implémentation avec Supabase Edge Functions:**
\`\`\`typescript
// Edge Function pour analyse IA
export default async function analyzeRequest(req: Request) {
  const { requestId } = await req.json();
  
  // Récupération des données depuis Supabase
  const { data: request } = await supabase
    .from('requests')
    .select('*, files(*)')
    .eq('id', requestId)
    .single();
    
  // Analyse IA et mise à jour
  const analysis = await performAIAnalysis(request);
  
  await supabase
    .from('requests')
    .update({ ai_summary: analysis.summary })
    .eq('id', requestId);
}
\`\`\`

**Intégrations IA:**
- **Vision AI:** GPT-4V ou Claude 3 Sonnet pour analyse d'images
- **NLP:** Modèles spécialisés pour extraction d'entités métier
- **OCR:** Tesseract pour lecture de plans PDF scannés

**Pipeline d'Analyse:**
\`\`\`mermaid
graph LR
    A[Demande Client] --> B[Preprocessing]
    B --> C[Text Analysis]
    B --> D[Image Analysis]
    B --> E[Plan Analysis]
    C --> F[Entity Extraction]
    D --> G[Material Recognition]
    E --> H[Surface Calculation]
    F --> I[Cost Estimation]
    G --> I
    H --> I
    I --> J[Structured Quote]
\`\`\`

### 4. Moteur de Prix (Price Engine)

**Responsabilités:**
- Gestion du catalogue produits
- Synchronisation avec APIs fournisseurs
- Calcul des coûts avec facteurs de complexité
- Gestion des marges et remises

**Catalogue Produits Existant:**

La table `products_catalog` contient déjà :
- Informations produits complètes (nom, description, catégorie)
- Prix actuels et coûts fournisseurs
- Gestion des stocks et disponibilité
- Recherche full-text intégrée
- Support multi-régions et fournisseurs

**Fonctionnalités du Catalogue:**
\`\`\`typescript
// Recherche produits avec Supabase
const searchProducts = async (query: string, category?: string) => {
  let queryBuilder = supabase
    .from('products_catalog')
    .select('*')
    .eq('is_active', true);
    
  if (category) {
    queryBuilder = queryBuilder.eq('category', category);
  }
  
  if (query) {
    queryBuilder = queryBuilder.textSearch('name,description', query);
  }
  
  return queryBuilder;
};
\`\`\`

### 5. Service de Notifications (Edge Functions + Supabase)

**Responsabilités:**
- Envoi d'emails transactionnels
- Notifications en temps réel via Supabase Realtime
- Gestion des templates d'emails
- Suivi des accusés de réception dans table `notifications`

**Implémentation:**
\`\`\`typescript
// Edge Function pour notifications
export default async function sendNotification(req: Request) {
  const { type, requestId, quoteId, recipientEmail } = await req.json();
  
  // Enregistrement en base
  const { data: notification } = await supabase
    .from('notifications')
    .insert({
      type,
      request_id: requestId,
      quote_id: quoteId,
      recipient_email: recipientEmail,
      status: 'pending'
    })
    .select()
    .single();
    
  // Envoi email via service externe
  await sendEmail(notification);
}
\`\`\`

**Technologies:**
- Supabase Edge Functions pour la logique
- Supabase Realtime pour notifications temps réel
- Service email externe (SendGrid/Resend)

### 6. Gestion des Fichiers (Supabase Storage)

**Responsabilités:**
- Upload sécurisé via Supabase Storage
- Redimensionnement automatique d'images
- Génération de thumbnails
- CDN intégré Supabase

**Configuration Supabase Storage:**
\`\`\`typescript
// Upload avec Supabase Storage
const uploadFile = async (file: File, requestId: string) => {
  const fileName = `${requestId}/${Date.now()}-${file.name}`;
  
  const { data, error } = await supabase.storage
    .from('quote-files')
    .upload(fileName, file, {
      cacheControl: '3600',
      upsert: false
    });
    
  if (!error) {
    // Enregistrement en base
    await supabase.from('files').insert({
      request_id: requestId,
      name: file.name,
      type: file.type,
      size: file.size,
      url: data.path
    });
  }
};
\`\`\`

**Buckets Supabase:**
- `quote-files` : Fichiers clients (photos, plans)
- `quote-documents` : Devis et factures générés
- `thumbnails` : Miniatures automatiques

## Modèles de Données

### Modèles TypeScript basés sur les Tables Supabase

\`\`\`typescript
// Basé sur la table 'requests'
interface Request {
  id: string;
  created_at: string;
  updated_at: string;
  status: 'en_attente' | 'en_analyse' | 'en_cours' | 'validé' | 'envoyé' | 'accepté' | 'refusé';
  priority: number;
  client_id?: string;
  client_name?: string;
  client_email?: string;
  input: Record<string, any>; // Données JSON flexibles
  previews?: Record<string, any>;
  ai_summary?: string;
  quote?: Record<string, any>;
  invoice_no?: string;
}

// Basé sur la table 'files'
interface File {
  id: string;
  request_id: string;
  name: string;
  type: string;
  size: number;
  url?: string;
  created_at: string;
}

interface AIAnalysis {
  textAnalysis: {
    extractedEntities: Entity[];
    detectedTrades: string[];
    complexityScore: number;
    confidence: number;
  };
  imageAnalysis: {
    detectedMaterials: Material[];
    conditionAssessment: string;
    estimatedDimensions: Dimension[];
  };
  planAnalysis?: {
    calculatedSurfaces: Surface[];
    identifiedRooms: Room[];
    structuralElements: Element[];
  };
  estimatedCosts: CostBreakdown;
}
\`\`\`

\`\`\`typescript
// Basé sur la table 'quotes'
interface Quote {
  id: string;
  request_id: string;
  version: number;
  status: 'brouillon' | 'en_revision' | 'validé' | 'envoyé' | 'accepté' | 'refusé';
  total_ht: number;
  total_ttc: number;
  tva_rate: number;
  margin_rate: number;
  created_by?: string;
  modified_by?: string;
  internal_comments?: string;
  template_id?: string;
  ai_generated_data?: Record<string, any>;
  custom_conditions?: string;
  validity_days: number;
  created_at: string;
  updated_at: string;
}

// Basé sur la table 'quote_items'
interface QuoteItem {
  id: string;
  quote_id: string;
  category: string;
  subcategory?: string;
  description: string;
  quantity: number;
  unit: string;
  unit_price: number;
  material_cost: number;
  labor_cost: number;
  complexity_factor: number;
  total_price: number; // Calculé automatiquement
  order_index: number;
  is_optional: boolean;
  supplier?: string;
  notes?: string;
  ai_suggested: boolean;
  created_at: string;
  updated_at: string;
}

// Basé sur la table 'invoices'
interface Invoice {
  id: string;
  invoice_number: string;
  quote_id: string;
  request_id: string;
  status: 'brouillon' | 'envoyée' | 'payée' | 'annulée';
  client_name: string;
  client_email: string;
  client_address?: string;
  client_siret?: string;
  total_ht: number;
  total_ttc: number;
  tva_amount: number;
  tva_rate: number;
  due_date?: string;
  payment_terms: number;
  items: Record<string, any>[];
  created_by: string;
  created_at: string;
  updated_at: string;
}
\`\`\`

## Gestion des Erreurs

### Stratégie de Gestion d'Erreurs

**Codes d'Erreur Standardisés:**
\`\`\`typescript
enum ErrorCodes {
  // Erreurs client (4xx)
  INVALID_INPUT = 'INVALID_INPUT',
  UNAUTHORIZED = 'UNAUTHORIZED',
  FORBIDDEN = 'FORBIDDEN',
  NOT_FOUND = 'NOT_FOUND',
  FILE_TOO_LARGE = 'FILE_TOO_LARGE',
  
  // Erreurs serveur (5xx)
  AI_SERVICE_ERROR = 'AI_SERVICE_ERROR',
  DATABASE_ERROR = 'DATABASE_ERROR',
  EXTERNAL_API_ERROR = 'EXTERNAL_API_ERROR',
  INTERNAL_ERROR = 'INTERNAL_ERROR'
}
\`\`\`

**Middleware de Gestion d'Erreurs:**
\`\`\`typescript
const errorHandler = (error: Error, req: Request, res: Response, next: NextFunction) => {
  const errorResponse = {
    code: error.code || 'INTERNAL_ERROR',
    message: error.message,
    timestamp: new Date().toISOString(),
    requestId: req.headers['x-request-id']
  };
  
  // Log pour monitoring
  logger.error('API Error', errorResponse);
  
  res.status(getHttpStatus(error.code)).json(errorResponse);
};
\`\`\`

### Fallbacks et Résilience

**Circuit Breaker pour Services IA:**
- Timeout de 30 secondes pour les analyses IA
- Fallback vers analyse simplifiée si échec
- Retry automatique avec backoff exponentiel

**Gestion des Pannes:**
- Sauvegarde automatique des brouillons
- Mode dégradé sans IA si nécessaire
- Synchronisation différée des catalogues

## Stratégie de Test

### Tests Unitaires
- Couverture minimale de 80%
- Tests des services métier
- Mocking des dépendances externes

### Tests d'Intégration
- Tests des APIs REST
- Tests de la base de données
- Tests des intégrations IA

### Tests End-to-End
- Parcours client complet
- Workflow professionnel
- Tests de performance

**Configuration Jest:**
\`\`\`typescript
// jest.config.js
module.exports = {
  testEnvironment: 'node',
  coverageThreshold: {
    global: {
      branches: 80,
      functions: 80,
      lines: 80,
      statements: 80
    }
  },
  setupFilesAfterEnv: ['<rootDir>/tests/setup.ts']
};
\`\`\`

### Tests de Charge
- 100 utilisateurs simultanés
- Analyse IA sous charge
- Temps de réponse < 3 secondes

## Sécurité avec Supabase

### Authentification et Autorisation
- Supabase Auth avec JWT automatique
- Row Level Security (RLS) sur toutes les tables
- Politiques RLS par rôle (client/pro/admin)
- Rate limiting intégré Supabase

### Protection des Données
- Chiffrement automatique Supabase
- HTTPS obligatoire (TLS 1.3)
- Validation avec Supabase Schema
- Storage sécurisé avec politiques RLS

### Politiques RLS Exemple
\`\`\`sql
-- Politique pour la table requests
CREATE POLICY "Users can view own requests" ON requests
  FOR SELECT USING (
    auth.uid() = client_id OR 
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role = 'pro'
    )
  );

-- Politique pour la table quotes
CREATE POLICY "Professionals can manage quotes" ON quotes
  FOR ALL USING (
    EXISTS (
      SELECT 1 FROM profiles 
      WHERE id = auth.uid() AND role IN ('pro', 'admin')
    )
  );
\`\`\`

### Monitoring et Audit
- Historique automatique avec `quote_history`
- Logs Supabase intégrés
- Triggers pour audit trail
- Backup automatique Supabase

Cette conception adaptée à Supabase fournit une base solide et évolutive pour votre application de devis automatisés. L'architecture tire parti des fonctionnalités intégrées de Supabase tout en permettant un développement par phases et une maintenance facilitée.
