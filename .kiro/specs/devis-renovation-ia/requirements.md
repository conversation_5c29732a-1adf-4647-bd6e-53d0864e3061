# Document des Exigences - Application IA Devis Rénovation

## Introduction

L'application de devis de rénovation automatisés est une plateforme web intelligente déjà en développement qui révolutionne le processus de création de devis dans le secteur du bâtiment. Basée sur Next.js 14 et Supabase, elle utilise l'intelligence artificielle (Google Gemini) pour analyser automatiquement les demandes clients et génère des devis précis que les professionnels peuvent valider et ajuster.

**État actuel :** La Phase 1 est largement implémentée avec l'interface client, l'analyse IA avec fallback, l'interface professionnelle complète, le système de devis avancé, les templates et l'historique. Les exigences suivantes définissent les améliorations et fonctionnalités manquantes pour finaliser le système.

## Exigences

### Exigence 1 - Amélioration Interface Client (Partiellement Implémentée)

**User Story:** En tant que client, je veux une interface de soumission optimisée avec validation avancée et feedback en temps réel, afin d'améliorer l'expérience utilisateur.

#### Critères d'Acceptation

1. QUAND un client saisit sa description ALORS le système DOIT fournir des suggestions automatiques basées sur les mots-clés détectés
2. QUAND un client upload des fichiers ALORS le système DOIT afficher une prévisualisation avec détection automatique du type de contenu (photo vs plan)
3. QUAND un client soumet sa demande ALORS le système DOIT afficher une estimation de délai de traitement basée sur la complexité détectée
4. QUAND une demande est soumise ALORS le système DOIT envoyer une confirmation par email avec lien de suivi
5. SI l'analyse IA échoue ALORS le système DOIT basculer automatiquement sur le système de règles de fallback

### Exigence 2 - Optimisation Moteur IA (Partiellement Implémentée)

**User Story:** En tant que système IA, je veux améliorer la précision d'analyse et intégrer la recherche de prix en temps réel, afin de fournir des estimations plus précises.

#### Critères d'Acceptation

1. QUAND l'analyse IA est lancée ALORS le système DOIT utiliser Google Gemini avec fallback sur règles métier si indisponible
2. QUAND des prix sont nécessaires ALORS le système DOIT interroger Google Programmable Search ou Shopping Content API avec cache TTL
3. QUAND les quotas API sont atteints ALORS le système DOIT implémenter un backoff exponentiel et utiliser les prix en cache
4. QUAND l'analyse est terminée ALORS le système DOIT stocker les résultats dans le champ `ai_generated_data` de la table quotes
5. SI l'IA détecte des incohérences ALORS le système DOIT générer des alertes pour validation manuelle

### Exigence 3 - Amélioration Génération de Devis (Implémentée - Optimisations Requises)

**User Story:** En tant que système, je veux optimiser la génération automatique de devis avec des calculs plus précis et une meilleure intégration du catalogue produits.

#### Critères d'Acceptation

1. QUAND un devis est généré ALORS le système DOIT utiliser les triggers automatiques pour calculer les totaux via `update_quote_totals()`
2. QUAND des produits sont sélectionnés ALORS le système DOIT prioriser les produits du catalogue `products_catalog` avec prix actualisés
3. QUAND les coûts sont calculés ALORS le système DOIT séparer clairement `material_cost` et `labor_cost` dans `quote_items`
4. QUAND un facteur de complexité est appliqué ALORS le système DOIT l'enregistrer dans `complexity_factor` pour traçabilité
5. QUAND le devis est finalisé ALORS le système DOIT créer automatiquement une entrée dans `quote_history` avec l'action 'created'

### Exigence 4 - Finalisation Interface Professionnel (Largement Implémentée)

**User Story:** En tant que professionnel, je veux des fonctionnalités avancées de gestion et d'analyse pour optimiser mon workflow et mes performances.

#### Critères d'Acceptation

1. QUAND j'accède au dashboard ALORS le système DOIT afficher des statistiques de conversion, CA et performance basées sur les données historiques
2. QUAND je modifie un devis ALORS le système DOIT enregistrer automatiquement l'historique dans `quote_history` avec IP et User-Agent
3. QUAND je compare des versions ALORS le système DOIT afficher les différences entre `previous_values` et `new_values`
4. QUAND j'utilise un template ALORS le système DOIT incrémenter automatiquement le `usage_count` dans la table `templates`
5. SI je dépasse certains seuils d'écart ALORS le système DOIT déclencher des alertes intelligentes configurables

### Exigence 5 - Optimisation Système de Templates (Implémenté - Améliorations Requises)

**User Story:** En tant que professionnel, je veux des fonctionnalités avancées de templates avec partage et analytics, afin d'optimiser ma productivité.

#### Critères d'Acceptation

1. QUAND je crée un template ALORS le système DOIT permettre de le marquer comme `is_public` pour partage avec d'autres professionnels
2. QUAND j'utilise un template public ALORS le système DOIT incrémenter le `usage_count` pour statistiques d'adoption
3. QUAND je recherche des templates ALORS le système DOIT utiliser l'index full-text sur `name` et `description`
4. QUAND j'applique un template ALORS le système DOIT adapter automatiquement les prix selon la région du professionnel
5. SI un template est très utilisé ALORS le système DOIT le suggérer en priorité dans l'interface

### Exigence 6 - Finalisation Système de Notifications (Partiellement Implémentée)

**User Story:** En tant que professionnel, je veux un système de notifications complet avec suivi avancé et relances automatiques, afin d'optimiser mon taux de conversion.

#### Critères d'Acceptation

1. QUAND j'envoie un devis ALORS le système DOIT créer automatiquement une notification avec `type='envoi'` et `status='pending'`
2. QUAND le client ouvre le devis ALORS le système DOIT mettre à jour `read_at` et créer une notification `type='lecture'`
3. QUAND une relance est programmée ALORS le système DOIT utiliser `scheduled_at` avec retry logic basé sur `retry_count` et `max_retries`
4. QUAND une notification échoue ALORS le système DOIT enregistrer la `failed_reason` et programmer un nouveau tentative
5. SI le client interagit avec le devis ALORS le système DOIT enregistrer `clicked_at` et `delivered_at` pour analytics

### Exigence 7 - Amélioration Système de Facturation (Implémenté - Optimisations Requises)

**User Story:** En tant que professionnel, je veux des fonctionnalités avancées de facturation avec export comptable et gestion fiscale, afin de simplifier ma gestion administrative.

#### Critères d'Acceptation

1. QUAND un devis est accepté ALORS le système DOIT créer automatiquement une facture avec `invoice_number` unique et `fiscal_year` via trigger
2. QUAND une facture est générée ALORS le système DOIT copier les `items` depuis `quote_items` avec calculs TVA automatiques
3. QUAND j'exporte pour la comptabilité ALORS le système DOIT marquer `accounting_exported=true` avec `accounting_export_date`
4. QUAND le paiement est reçu ALORS le système DOIT mettre à jour `paid_at` et `payment_reference`
5. SI l'année fiscale change ALORS le système DOIT réinitialiser automatiquement la numérotation via le trigger `set_fiscal_year()`

### Exigence 8 - Optimisation Catalogue Produits (Implémenté - Synchronisation API Requise)

**User Story:** En tant que système, je veux une synchronisation automatique des prix avec les APIs externes et une gestion intelligente du cache, afin de garantir des estimations précises et à jour.

#### Critères d'Acceptation

1. QUAND une synchronisation de prix est lancée ALORS le système DOIT utiliser Google Shopping Content API avec respect des quotas et backoff
2. QUAND des prix sont mis à jour ALORS le système DOIT mettre à jour `last_price_update` et conserver l'historique
3. QUAND un produit change de `availability` ALORS le système DOIT notifier les devis en cours utilisant ce produit
4. QUAND je recherche des produits ALORS le système DOIT utiliser l'index full-text et les filtres par `category`, `region`, `is_active`
5. SI un prix varie de plus de 20% ALORS le système DOIT générer une alerte pour validation manuelle

### Exigence 9 - Renforcement Sécurité et Conformité (Partiellement Implémentée)

**User Story:** En tant qu'utilisateur, je veux des fonctionnalités avancées de sécurité et de conformité RGPD, afin de garantir la protection de mes données.

#### Critères d'Acceptation

1. QUAND un utilisateur se connecte ALORS le système DOIT utiliser Supabase Auth avec RLS activé sur toutes les tables
2. QUAND des données sensibles sont accédées ALORS le système DOIT vérifier les politiques RLS par rôle (client/pro/admin)
3. QUAND des modifications sont apportées ALORS le système DOIT enregistrer `ip_address` et `user_agent` dans `quote_history`
4. QUAND un client demande ses données ALORS le système DOIT fournir un export complet via les relations de clés étrangères
5. SI un client demande la suppression ALORS le système DOIT utiliser les contraintes CASCADE pour effacement complet

### Exigence 10 - Optimisation Performance et Monitoring (Partiellement Implémentée)

**User Story:** En tant qu'utilisateur, je veux des performances optimales avec monitoring avancé et récupération automatique, afin de garantir une expérience fluide.

#### Critères d'Acceptation

1. QUAND l'analyse IA est lancée ALORS le système DOIT utiliser le cache TTL pour éviter les re-calculs inutiles
2. QUAND les APIs externes sont lentes ALORS le système DOIT implémenter un timeout et basculer sur le cache
3. QUAND je navigue dans l'interface ALORS le système DOIT utiliser Next.js App Router avec optimisations automatiques
4. QUAND des erreurs surviennent ALORS le système DOIT utiliser les triggers de sauvegarde automatique existants
5. SI les quotas API sont atteints ALORS le système DOIT implémenter un backoff exponentiel et notifier l'administrateur

### Exigence 11 - Nouvelles Fonctionnalités Phase 2

**User Story:** En tant qu'utilisateur, je veux accéder aux fonctionnalités avancées de la Phase 2 pour étendre les capacités du système.

#### Critères d'Acceptation

1. QUAND je veux signer un devis ALORS le système DOIT intégrer une solution de signature électronique
2. QUAND j'exporte vers la comptabilité ALORS le système DOIT supporter les intégrations Sage, Ciel, et autres logiciels métier
3. QUAND je planifie un chantier ALORS le système DOIT fournir des outils de gestion de planning intégrés
4. QUAND j'utilise l'application mobile ALORS elle DOIT être développée en React Native avec synchronisation offline
5. SI je veux analyser mes performances ALORS le système DOIT fournir des rapports avancés et analytics prédictives
