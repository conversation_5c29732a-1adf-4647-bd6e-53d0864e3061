# Application BTP - Devis Rénovation Automatisés

Application web intelligente qui automatise la création de devis de rénovation en analysant les descriptions clients, plans architecturaux et photos via IA, tout en permettant l'ajustement manuel par le professionnel.

## 🚀 Fonctionnalités

### Interface Client
- **Soumission de demandes** : Formulaire guidé avec description détaillée
- **Upload de fichiers** : Photos multiples, plans architecturaux (PDF, images)
- **Analyse automatique** : IA pour extraction d'informations et estimation

### Interface Professionnel
- **Tableau de bord complet** : Vue d'ensemble des demandes et statistiques
- **Éditeur de devis avancé** : Modification granulaire des postes
- **Système de versions** : Historique complet des modifications
- **Templates réutilisables** : Bibliothèque personnelle de devis
- **Gestion des notifications** : Relances automatiques programmables
- **Conversion automatique** : Devis → Factures

### Moteur d'IA
- **Analyse textuelle** : Extraction d'entités (matériaux, surfaces, types de travaux)
- **Analyse visuelle** : Reconnaissance d'éléments sur photos et plans
- **Recherche de prix** : Programmable Search (Custom Search JSON API) ou Google Shopping Content API, avec cache (TTL), limitation de débit et backoff pour respecter les quotas et CGU
- **Calcul automatique** : Base de données produits et barèmes main-d'œuvre

## IA & Services externes
- **Google Programmable Search (Custom Search JSON API)** ou **Google Shopping Content API** pour les prix actualisés
- **Mise en cache** (TTL), **limitation de débit** et **backoff** pour éviter les dépassements de quotas
## 🏗️ Architecture

### Frontend
- **Next.js 14** avec App Router
- **TypeScript** pour la sécurité des types
- **Tailwind CSS** + shadcn/ui pour l'interface
- **React Hook Form** pour la gestion des formulaires

### Backend & Base de données
- **Supabase** (PostgreSQL + Auth + Storage + RLS)
- **9 tables relationnelles** pour une gestion complète
- **Row Level Security** pour la sécurité multilocataire
- **Triggers automatiques** pour les calculs et l'historique

### IA & Services externes
- **Vercel AI SDK** pour l'intégration IA
- **Google Gemini** pour l'analyse avancée
- **Google Search API** pour les prix actualisés
- **Système de secours basé sur des règles** si IA indisponible

## 📊 Schéma de base de données

### Tables principales

#### `profiles` - Gestion utilisateurs
- Rôles : `client`, `pro`, `admin`
- Authentification Supabase intégrée

#### `requests` - Demandes clients
- Workflow : `en_attente` → `en_analyse` → `en_cours` → `validé` → `envoyé` → `accepté/refusé`
- Stockage des fichiers et analyse IA

#### `quotes` - Devis professionnels
- Versioning automatique
- Statuts indépendants du workflow demande
- Traçabilité complète des modifications

#### `quote_items` - Postes de devis
- Modification granulaire (quantité, prix, complexité)
- Calculs automatiques via triggers
- Support matériaux + main d'œuvre

#### `templates` - Bibliothèque pro
- Templates réutilisables par catégorie
- Partage public/privé
- Statistiques d'utilisation

#### `products_catalog` - Catalogue produits
- Prix actualisés par région
- Intégration fournisseurs
- Synchronisation API

#### `invoices` - Facturation
- Conversion automatique depuis devis acceptés
- Numérotation automatique
- Export comptable

#### `notifications` - Relances
- Programmation automatique
- Suivi des lectures et interactions
- Retry logic intégré

#### `quote_history` - Historique
- Traçabilité complète des modifications
- Comparaison entre versions
- Audit trail avec IP/User-Agent

## 🛠️ Installation

### Prérequis
- Node.js 18+
- Compte Supabase
- Clé API Google Gemini (optionnel)

### Configuration

1. **Clone du projet**
\`\`\`bash
git clone <repo-url>
cd v0-btp
npm install
\`\`\`

2. **Variables d'environnement**
\`\`\`bash
cp .env.example .env.local
# Compléter avec vos clés API
\`\`\`

3. **Base de données Supabase**
Les migrations doivent être appliquées sur votre propre projet Supabase (ne publiez jamais d'identifiants de projet).

4. **Démarrage**
\`\`\`bash
npm run dev
\`\`\`

### Variables d'environnement requises

\`\`\`env
# Supabase (requis)
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key

# Supabase (serveur uniquement — ne jamais committer ni exposer)
# Utilisé côté serveur pour migrations/cron — jamais dans le navigateur.
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key

# IA (optionnel - fallback basé sur des règles)
# Clé serveur uniquement (ne pas préfixer par NEXT_PUBLIC)
GEMINI_API_KEY=your_gemini_key

\`\`\`

## 📱 Utilisation

### Côté Client
1. **Soumission** : `/` - Formulaire de demande
2. **Upload** : Glisser-déposer photos et plans
3. **Validation** : Vérification automatique des données

### Côté Professionnel
1. **Tableau de bord** : `/pro/dashboard` - Vue d'ensemble
2. **Prise en charge** : Assignation des demandes
3. **Création devis** : Depuis template ou vierge
4. **Édition** : `/pro/quote-editor/[id]` - Interface côte-à-côte
5. **Validation et envoi** : Workflow complet jusqu'à la facturation

## 🔧 Fonctionnalités avancées

### Système de templates
- **Création** depuis devis existants
- **Réutilisation** avec adaptation automatique
- **Partage** entre professionnels
- **Statistiques** d'utilisation

### Éditeur de devis professionnel
- **Vue côte-à-côte** : IA vs version modifiable
- **Modification en temps réel** avec recalculs automatiques
- **Alertes intelligentes** : écarts importants vs IA
- **Historique complet** : qui a modifié quoi et quand

### Notifications automatiques
- **Relances programmables** après envoi devis
- **Suivi des lectures** et interactions client
- **Escalade automatique** selon règles métier

### Analyses et rapports
- **Statistiques du tableau de bord** : conversion, CA, performance
- **Comparaisons** entre versions de devis
- **Audit trail** complet pour conformité

## 🔒 Sécurité

### Row Level Security (RLS)
- **Isolation par rôle** : clients ne voient que leurs demandes
- **Professionnels** : accès limité à leurs devis
- **Admins** : accès complet avec audit

### Authentification
- **Supabase Auth** intégré
- **Gestion des rôles** automatique
- **Sessions sécurisées** avec JWT

## 🚀 Déploiement

### Vercel (recommandé)
\`\`\`bash
# Auto-déploiement depuis GitHub
# Variables d'environnement à configurer dans Vercel
\`\`\`

### Docker (alternatif)
\`\`\`bash
# Dockerfile inclus pour déploiement containerisé
docker build -t btp-app .
docker run -p 3000:3000 btp-app
\`\`\`

## 📈 Roadmap

### Phase 1 (Actuelle)
- [x] Interface client de soumission
- [x] Analyse IA avec fallback
- [x] Interface pro complète
- [x] Système de devis avancé
- [x] Templates et historique

### Phase 2 (Prochaine)
- [ ] Application mobile (React Native)
- [ ] Intégration comptabilité (Sage, etc.)
- [ ] Signature électronique des devis
- [ ] Planning et gestion de chantier

### Phase 3 (Future)
- [ ] Marketplace de professionnels
- [ ] IA prédictive pour les prix
- [ ] Réalité augmentée pour les mesures
- [ ] Blockchain pour la traçabilité

## 🤝 Contribution

1. Fork le projet
2. Créer une branche feature (`git checkout -b feature/nouvelle-fonctionnalité`)
3. Commit (`git commit -am 'Ajout nouvelle fonctionnalité'`)
4. Push (`git push origin feature/nouvelle-fonctionnalité`)
5. Pull Request

## 📄 Licence

MIT License - voir le fichier LICENSE pour plus de détails.

## 🆘 Support

- **Issues GitHub** pour les bugs et demandes de fonctionnalités
- **Discussions** pour les questions générales
- **Wiki** pour la documentation détaillée

---

### Développé avec ❤️ pour révolutionner l'estimation BTP
