Tâches restantes et améliorations recommandées
=============================================

1) Validation et UX
   - Ajouter une validation front-end pour le SIRET (format et longueur).
   - Afficher des messages d'erreur plus détaillés côté UI pour les échecs d'updateProfile.
   - Ajouter un indicateur visuel dans l'éditeur de devis si le profil pro est incomplet.

2) Tests
   - Ajouter des tests unitaires pour `use-auth` (mock supabase) pour vérifier :
     - getUser récupère et crée un profile si absent
     - updateProfile met à jour les champs company
   - Tests E2E rapides pour :
     - Remplir le profil pro -> créer/un éditer un devis -> vérifier que la prévisualisation reprend les champs.

3) Sécurité & Permissions
   - Vérifier les politiques Row Level Security (RLS) dans Supabase pour s'assurer que
     seuls les utilisateurs peuvent modifier leur profil et que les pros ont accès à leurs propres ressources.

4) Backend / Migration
   - Examiner si des contraintes (format SIRET, longueur) ou index sont nécessaires sur `profiles.siret`.
   - Ajouter une migration pour peupler automatiquement `company_name` à partir du champ `name` si souhaité.

5) Améliorations facultatives
   - Ajouter un endpoint API pour récupérer le profil pro côté server (SSR) si utile.
   - Permettre l'upload d'un logo d'entreprise et l'afficher dans la prévisualisation.

6) Déploiement
   - Après tests, inclure la migration dans le pipeline CI/CD pour exécution sur les environnements de staging/production.

Prochaine étape proposée
-----------------------

1) Rendre l'opération de création de `request` + `files` atomique
   - Créer une fonction SQL (RPC) dans Postgres qui effectue l'insertion de la ligne
     `requests` et des `files` associés à l'intérieur d'une transaction (BEGIN/COMMIT/ROLLBACK).
   - Appeler cette RPC depuis `app/actions/process-request.ts` au lieu d'appeler deux inserts séparés.
   - Avantage : échec partiel impossible — soit tout est inséré, soit rien n'est persistant.

2) Tests et vérifications
   - Ajouter un test qui simule un échec lors de l'insertion des fichiers et vérifie que
     la demande n'est pas persistée (ou est marquée `failed` si on choisit cette stratégie).
   - Ajouter un test d'intégration légers pour valider la RPC (si implémentée) en environnement de test.

3) Plan de déploiement
   - Ajouter la migration SQL contenant la fonction RPC et la déployer sur staging.
   - Exécuter les tests d'intégration, puis déployer en production.
Priorité recommandée : 1 -> 2 -> 3 -> 4 -> 5 -> 6
