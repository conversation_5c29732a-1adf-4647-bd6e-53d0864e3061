/**
 * Tests for analyzeRequest in app/actions/analyze.ts
 * Testing library/framework: Vitest
 *
 * Scenarios:
 * - Gemini happy path with DB save
 * - Gemini: DB save failure handled
 * - Gemini: AI timeout -> fallback to rule-based with warning
 * - Gemini: generic AI error -> fallback to rule-based with error log
 * - Gemini: no DB save when requestId/userId missing
 * - Rule-based when GEMINI_KEY missing + DB save
 * - Rule-based: DB save error handled
 * - Totals and category fallback validation
 */

import { afterEach, beforeEach, describe, expect, it, vi } from 'vitest'
import type { ClientInput } from '@/lib/types'
import { DEFAULT_MARGIN } from '@/lib/quote-constants'

// (moved to beforeEach via dynamic import to avoid env capture)
let analyzeRequest: (typeof import('./analyze'))['analyzeRequest']

// Mock modules used by analyzeRequest
vi.mock('@/lib/ai-utils', () => {
  return {
    generateTextWithTimeout: vi.fn(),
    generateObjectWithTimeout: vi.fn(),
  }
})

vi.mock('@/lib/quote', () => {
  return {
    buildQuote: vi.fn((input: ClientInput) => {
      const items = [
        {
          id: 'rb_1',
          label: 'Rule-based item',
          category: input.typeRenovation ?? 'general',
          quantity: 10,
          unit: 'm2',
          unitPrice: 25,
          total: 250,
          editable: true,
        },
      ]
      const subtotal = 250
      const margin = Math.round(subtotal * DEFAULT_MARGIN * 100) / 100
      const vatRate = 0.2
      const vatAmount = Math.round((subtotal + margin) * vatRate * 100) / 100
      const total = Math.round((subtotal + margin + vatAmount) * 100) / 100
      return {
        id: 'q_rule',
        currency: 'EUR',
        items,
        totals: { subtotal, discount: 0, margin, vatRate, vatAmount, total },
        notes: 'Analyse par règles métier',
        aiEstimateTotal: total,
        aiConfidence: 0.5,
      }
    }),
    recalcTotals: vi.fn(),
  }
})

vi.mock('@/lib/database', () => {
  return {
    createQuoteFromAI: vi.fn(),
  }
})

// Mock @ai-sdk/google (construction only; behavior driven by ai-utils mocks)
vi.mock('@ai-sdk/google', () => {
  const googleFn = vi.fn((modelName: string) => ({ modelName }))
  Object.assign(googleFn, {
    tools: {
      googleSearch: vi.fn(() => vi.fn()),
    },
  })
  return { google: googleFn }
})

const ORIGINAL_ENV = { ...process.env }

beforeEach(async () => {
  vi.resetModules()
  process.env = { ...ORIGINAL_ENV }
  ;({ analyzeRequest } = await import('./analyze'))
})

afterEach(() => {
  vi.clearAllMocks()
  vi.restoreAllMocks()
  process.env = { ...ORIGINAL_ENV }
  vi.useRealTimers()
})

const basePayload = {
  input: {
    typeRenovation: 'plomberie' as const,
    description: 'Remplacement de tuyauterie cuisine',
    localisation: 'Lyon',
    urgence: 'normale' as const,
    surfaceM2: 12,
  },
  requestId: 'req_123',
  userId: 'user_456',
}

describe('analyzeRequest', () => {
  it('uses Gemini path when GEMINI_KEY is set, builds AI-based quote and saves to DB', async () => {
    process.env.GEMINI_KEY = 'test_key'

    const { generateTextWithTimeout, generateObjectWithTimeout } = await import('@/lib/ai-utils')
    const { createQuoteFromAI } = await import('@/lib/database')

    ;(generateTextWithTimeout as any).mockResolvedValue({ text: 'prix actuels trouvés' })

    const aiItems = [
      { label: 'Démontage ancien', category: 'plomberie', quantity: 3, unit: 'h', unitPrice: 45 },
      { label: 'Pose tuyaux PER', category: '', quantity: 20, unit: 'ml', unitPrice: 8.75 },
    ]
    ;(generateObjectWithTimeout as any).mockResolvedValue({
      object: { summary: 'Travaux plomberie avec matériaux et MO.', items: aiItems },
    })

    const dbReturn = { id: 'db_q_1', items: aiItems, summary: 'Travaux...', totals: { total: 0 } };
    (createQuoteFromAI as any).mockResolvedValue(dbReturn)

    const result = await analyzeRequest({ ...basePayload, persist: true })

    expect(result.usedAIProvider).toBe('gemini')
    expect(result.aiSummary).toBe('Travaux plomberie avec matériaux et MO.')
    expect(result.quote.items).toHaveLength(2)
    const [i0, i1] = result.quote.items
    expect(i0.total).toBeCloseTo(135, 2)
    expect(i1.category).toBe(basePayload.input.typeRenovation)

    const subtotal = Math.round((3 * 45 + 20 * 8.75) * 100) / 100
    expect(subtotal).toBe(310)
    const margin = Math.round(subtotal * DEFAULT_MARGIN * 100) / 100
    expect(margin).toBe(46.5)
    const vatRate = 0.2
    const vatAmount = Math.round((subtotal + margin) * vatRate * 100) / 100
    expect(vatAmount).toBe(71.3)
    const total = Math.round((subtotal + margin + vatAmount) * 100) / 100

    expect(result.quote.totals).toEqual({
      subtotal,
      discount: 0,
      margin,
      vatRate,
      vatAmount,
      total,
    })
    expect(result.quote.aiEstimateTotal).toBe(total)

    expect(createQuoteFromAI).toHaveBeenCalledTimes(1)
    const [reqId, userId, payloadSaved] = (createQuoteFromAI as any).mock.calls[0] as [string, string, any]
    expect(reqId).toBe('req_123')
    expect(userId).toBe('user_456')
    expect(payloadSaved).toMatchObject({
      items: aiItems,
      summary: 'Travaux plomberie avec matériaux et MO.',
      aiEstimateTotal: total,
      aiConfidence: 0.7,
      usedProvider: 'gemini',
      searchData: {
        priceInfo: 'prix actuels trouvés',
      },
    })
    expect(result.databaseQuote).toBe(dbReturn)
  })

  it('logs and continues when DB save fails in Gemini path', async () => {
    process.env.GEMINI_KEY = 'test_key'
    const { generateTextWithTimeout, generateObjectWithTimeout } = await import('@/lib/ai-utils')
    const { createQuoteFromAI } = await import('@/lib/database')

    ;(generateTextWithTimeout as any).mockResolvedValue({ text: 'prix actuels' })
    ;(generateObjectWithTimeout as any).mockResolvedValue({
      object: { summary: 'Résumé', items: [{ label: 'Item', category: 'X', quantity: 1, unit: 'u', unitPrice: 100 }] },
    })

    const err = new Error('DB down');
    (createQuoteFromAI as any).mockRejectedValue(err)
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const result = await analyzeRequest({ ...basePayload, persist: true })
    expect(result.usedAIProvider).toBe('gemini')
    expect(result.databaseQuote).toBeUndefined()
    expect(consoleSpy).toHaveBeenCalledWith('Failed to save quote to database:', err)
    consoleSpy.mockRestore()
  })

  it('does not save to DB when persist is false or requestId missing in Gemini path', async () => {
    process.env.GEMINI_KEY = 'test_key'
    const { generateTextWithTimeout, generateObjectWithTimeout } = await import('@/lib/ai-utils')
    const { createQuoteFromAI } = await import('@/lib/database')

    ;(generateTextWithTimeout as any).mockResolvedValue({ text: 'prix actuels' })
    ;(generateObjectWithTimeout as any).mockResolvedValue({
      object: { summary: 'Résumé', items: [{ label: 'Item', category: 'X', quantity: 2, unit: 'u', unitPrice: 50 }] },
    })

    // Missing persist (defaults to false)
    await analyzeRequest({ ...basePayload, persist: false })
    // Missing requestId
    await analyzeRequest({ ...basePayload, requestId: undefined, persist: true })

    expect(createQuoteFromAI).not.toHaveBeenCalled()
  })

  it('saves to DB when persist is true and requestId present, using system user if userId missing', async () => {
    process.env.GEMINI_KEY = 'test_key'
    const { generateTextWithTimeout, generateObjectWithTimeout } = await import('@/lib/ai-utils')
    const { createQuoteFromAI } = await import('@/lib/database')

    ;(generateTextWithTimeout as any).mockResolvedValue({ text: 'prix actuels' })
    ;(generateObjectWithTimeout as any).mockResolvedValue({
      object: { summary: 'Résumé', items: [{ label: 'Item', category: 'X', quantity: 2, unit: 'u', unitPrice: 50 }] },
    })

    const dbReturn = { id: 'db_q_1' }
    ;(createQuoteFromAI as any).mockResolvedValue(dbReturn)

    const result = await analyzeRequest({ ...basePayload, userId: undefined, persist: true })

    expect(createQuoteFromAI).toHaveBeenCalledTimes(1)
    const [reqId, userId] = (createQuoteFromAI as any).mock.calls[0]
    expect(reqId).toBe('req_123')
    expect(userId).toBe('system')
    expect(result.databaseQuote).toBe(dbReturn)
  })

  it('falls back to rule-based when AI times out, with proper warning', async () => {
    process.env.GEMINI_KEY = 'test_key'
    const { generateTextWithTimeout } = await import('@/lib/ai-utils')
    ;(generateTextWithTimeout as any).mockRejectedValue(new Error('Operation timed out'))

    const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    const errorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const result = await analyzeRequest({ ...basePayload })
    expect(warnSpy).toHaveBeenCalledTimes(1)
    const [msg, err] = (warnSpy as vi.Mock).mock.calls[0] as [string, Error]
    expect(String(msg)).toContain('AI operation timed out. Falling back to rule-based quote.')
    expect(err).toBeInstanceOf(Error)
    expect(errorSpy).not.toHaveBeenCalled()
    expect(result.usedAIProvider).toBe('rule-based')
    expect(result.aiSummary).toBe('Analyse par règles métier')
    expect(result.quote.id).toBe('q_rule')

    warnSpy.mockRestore()
    errorSpy.mockRestore()
  })

  it('falls back to rule-based when AI throws generic error, logs error', async () => {
    process.env.GEMINI_KEY = 'test_key'
    const { generateTextWithTimeout } = await import('@/lib/ai-utils')
    ;(generateTextWithTimeout as any).mockRejectedValue(new Error('Network 500'))

    const warnSpy = vi.spyOn(console, 'warn').mockImplementation(() => {})
    const errorSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const result = await analyzeRequest({ ...basePayload })
    expect(warnSpy).not.toHaveBeenCalled()
    expect(errorSpy).toHaveBeenCalled()
    expect(result.usedAIProvider).toBe('rule-based')

    warnSpy.mockRestore()
    errorSpy.mockRestore()
  })

  it('uses rule-based path when GEMINI_KEY is absent and saves to DB', async () => {
    delete process.env.GEMINI_KEY
    const { createQuoteFromAI } = await import('@/lib/database')

    const dbReturn = { id: 'db_q_rule_1' }
    ;(createQuoteFromAI as any).mockResolvedValue(dbReturn)

    const result = await analyzeRequest({ ...basePayload, persist: true })
    expect(result.usedAIProvider).toBe('rule-based')
    expect(result.databaseQuote).toBeDefined()
    expect(createQuoteFromAI).toHaveBeenCalledTimes(1)
    const [, , payloadSaved] = (createQuoteFromAI as any).mock.calls[0]
    expect(payloadSaved).toMatchObject({
      items: expect.any(Array),
      summary: expect.any(String),
      aiEstimateTotal: expect.any(Number),
      aiConfidence: expect.any(Number),
      usedProvider: 'rule-based',
    })
  })

  it('handles DB save error in rule-based path gracefully', async () => {
    delete process.env.GEMINI_KEY
    const { createQuoteFromAI } = await import('@/lib/database')
    const err = new Error('DB write fail')
    ;(createQuoteFromAI as any).mockRejectedValue(err)
    const consoleSpy = vi.spyOn(console, 'error').mockImplementation(() => {})

    const result = await analyzeRequest({ ...basePayload, persist: true })
    expect(result.usedAIProvider).toBe('rule-based')
    expect(result.databaseQuote).toBeUndefined()
    expect(consoleSpy).toHaveBeenCalledWith('Failed to save quote to database:', err)
    consoleSpy.mockRestore()
  })
})
