"use server"

import { buildQuote } from "@/lib/quote"
import { createQuoteFromAI } from "@/lib/database"
import type { ClientInput, Quote, QuoteWithItems, Category, Unit } from "@/lib/types"
import { generateObjectWithTimeout, generateTextWithTimeout } from "@/lib/ai-utils"
import { z } from "zod"
import { google } from "@ai-sdk/google"
import { DEFAULT_MARGIN } from "@/lib/quote-constants"
import { DEFAULT_VAT } from "@/lib/catalog"

const AI_CONFIDENCE = 0.7
const AI_PROVIDER = "gemini"
const RULE_BASED_PROVIDER = "rule-based"

function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

type AIQuoteItem = {
  label: string
  category: Category
  quantity: number
  unit: Unit
  unitPrice: number
}

export interface AnalyzePayload {
  input: ClientInput
  requestId?: string
  userId?: string
  persist?: boolean
}

export async function analyzeRequest(payload: AnalyzePayload): Promise<{
  quote: Quote
  aiSummary: string
  usedAIProvider: typeof AI_PROVIDER | typeof RULE_BASED_PROVIDER
  databaseQuote?: QuoteWithItems
}> {
  let quote: Quote
  let aiSummary: string
  let usedAIProvider: typeof AI_PROVIDER | typeof RULE_BASED_PROVIDER
  let quoteDataForDb: {
    items: AIQuoteItem[]
    summary: string
    aiEstimateTotal: number
    aiConfidence: number
    usedProvider: typeof AI_PROVIDER | typeof RULE_BASED_PROVIDER
    searchData?: Record<string, unknown>
  }

  const hasGemini = !!(process.env.GOOGLE_GENERATIVE_AI_API_KEY || process.env.GEMINI_KEY)

  if (hasGemini) {
    try {
      // First step: Search for current BTP prices using Google Search
      const { text: priceInfo } = await generateTextWithTimeout({
        model: google("gemini-2.5-flash"),
        tools: {
          google_search: google.tools.googleSearch({}),
        },
        prompt: [
          "Recherche les prix actuels BTP 2024-2025 en France pour ce type de travaux.",
          "Trouve les prix moyens par m2, ml, unité ou heure selon le type de travaux.",
          "Concentre-toi sur la région si possible.",
          "",
          `Type de rénovation: ${payload.input.typeRenovation}`,
          `Description: ${payload.input.description}`,
          `Localisation: ${payload.input.localisation}`,
          "",
          "Recherche des prix récents et fiables pour ces travaux.",
        ].join("\n"),
      })

      // Second step: Use AI SDK to extract line items and summary from text with price context
      const schema = z.object({
        summary: z.string(),
        items: z
          .array(
            z.object({
              label: z.string(),
              category: z.string(),
              quantity: z.number().positive(),
              unit: z.enum(["m2", "ml", "u", "h"]),
              unitPrice: z.number().positive(),
            }),
          )
          .min(1),
      })

      const result = await generateObjectWithTimeout({
        model: google("gemini-2.5-flash"),
        schema,
        prompt: [
          "Tu es un estimateur BTP. À partir de la description du client et des informations de prix actuels, propose des postes (label, catégorie, quantité, unité, prix unitaire) cohérents en France.",
          "Unités: m2, ml, u, h. Prix réalistes (HT). Ajoute 1 poste main d'œuvre par corps.",
          "Utilise les informations de prix trouvées pour être plus précis.",
          "Retourne aussi un bref résumé de l'analyse.",
          "",
          `Description: ${payload.input.description}`,
          `Type de rénovation: ${payload.input.typeRenovation}`,
          `Urgence: ${payload.input.urgence}`,
          `Surface approx (m2): ${payload.input.surfaceM2 ?? "n/a"}`,
          `Localisation: ${payload.input.localisation}`,
          "",
          "Informations de prix actuels trouvées:",
          priceInfo,
        ].join("\n"),
      })

      if (!result.object) {
        throw new Error("AI analysis did not return the expected object.")
      }
      const object = result.object as any

      // Build a quote object from AI items
      const items = object.items.map((it: AIQuoteItem, idx: number) => ({
        id: `ai_${idx}`,
        label: it.label,
        category: it.category || payload.input.typeRenovation,
        quantity: it.quantity,
        unit: it.unit,
        unitPrice: it.unitPrice,
        total: Math.round(it.quantity * it.unitPrice * 100) / 100,
        editable: true,
      }))

      const subtotal =
        Math.round(items.reduce((sum: number, item: { total: number }) => sum + item.total, 0) * 100) / 100
      const margin = Math.round(subtotal * DEFAULT_MARGIN * 100) / 100
      const vatRate = DEFAULT_VAT
      const vatAmount = Math.round((subtotal + margin) * vatRate * 100) / 100
      const total = Math.round((subtotal + margin + vatAmount) * 100) / 100

      quote = {
        id: `q_${generateId()}`,
        currency: "EUR",
        items,
        totals: {
          subtotal,
          discount: 0,
          margin,
          vatRate,
          vatAmount,
          total,
        },
        notes: object.summary,
        aiEstimateTotal: total,
        aiConfidence: AI_CONFIDENCE,
      }
      aiSummary = object.summary
      usedAIProvider = AI_PROVIDER
      quoteDataForDb = {
        items: object.items.map((it: AIQuoteItem) => ({
          label: it.label,
          category: it.category,
          quantity: it.quantity,
          unit: it.unit,
          unitPrice: it.unitPrice,
        })),
        summary: object.summary,
        aiEstimateTotal: total,
        aiConfidence: AI_CONFIDENCE,
        usedProvider: AI_PROVIDER,
        searchData: {
          priceInfo: typeof priceInfo === "string" ? priceInfo.slice(0, 8000) : String(priceInfo),
        },
      }
    } catch (e) {
      let errorMessage = "An error occurred during AI analysis."
      if (e instanceof Error) {
        if (e.message.includes("timed out")) {
          errorMessage = "AI operation timed out. Falling back to rule-based quote."
        } else {
          errorMessage = `An error occurred during AI analysis: ${e.message}. Falling back to rule-based quote.`
        }
      }
      if (e.message.includes("timed out")) {
        console.warn(errorMessage, e)
      } else {
        console.error(errorMessage, e)
      }

      // Fallback to rule-based
      quote = buildQuote(payload.input)
      aiSummary = quote.notes || "Analyse IA indisponible."
      usedAIProvider = RULE_BASED_PROVIDER
      quoteDataForDb = {
        items: quote.items.map((it: any) => ({
          label: it.label,
          category: it.category,
          quantity: it.quantity,
          unit: it.unit,
          unitPrice: it.unitPrice,
        })),
        summary: quote.notes || "Analyse par règles métier",
        aiEstimateTotal: quote.aiEstimateTotal,
        aiConfidence: quote.aiConfidence,
        usedProvider: RULE_BASED_PROVIDER,
      }
    }
  } else {
    quote = buildQuote(payload.input)
    aiSummary = quote.notes || "Analyse IA indisponible."
    usedAIProvider = RULE_BASED_PROVIDER
    quoteDataForDb = {
      items: quote.items.map((it: any) => ({
        label: it.label,
        category: it.category,
        quantity: it.quantity,
        unit: it.unit,
        unitPrice: it.unitPrice,
      })),
      summary: quote.notes || "Analyse par règles métier",
      aiEstimateTotal: quote.aiEstimateTotal,
      aiConfidence: quote.aiConfidence,
      usedProvider: RULE_BASED_PROVIDER,
    }
  }

  const databaseQuote = await saveQuoteToDatabase(payload, quoteDataForDb)

  return {
    quote,
    aiSummary,
    usedAIProvider,
    databaseQuote,
  }
}

async function saveQuoteToDatabase(
  payload: AnalyzePayload,
  quoteData: {
    items: AIQuoteItem[]
    summary: string
    aiEstimateTotal: number
    aiConfidence: number
    usedProvider: typeof AI_PROVIDER | typeof RULE_BASED_PROVIDER
    searchData?: Record<string, unknown>
  },
): Promise<QuoteWithItems | undefined> {
  if (payload.persist && payload.requestId) {
    try {
      return await createQuoteFromAI(payload.requestId, payload.userId || "system", quoteData)
    } catch (error) {
      console.error("Failed to save quote to database:", error)
    }
  }
  return undefined
}
