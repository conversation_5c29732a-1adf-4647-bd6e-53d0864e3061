/**
 * Unit tests for app/actions/pro-actions.ts
 *
 * Testing framework: Vitest
 * These tests mock "@/lib/database" and verify:
 *  - Happy paths return data
 *  - Error handling behavior (throw vs. log + [] fallback)
 *  - Null/undefined data returns []
 *  - Correct Supabase query chain: from -> select("*") -> order("created_at", { ascending: false }) -> limit(N)
 */

import { describe, it, expect, vi, beforeEach } from "vitest"

// We mock the Supabase client module used by the code under test.
// The mock references a test-scoped variable (fromSpy) so each test can
// control and assert the query chain behavior.
let fromSpy: ReturnType<typeof vi.fn>
let builder: {
  select: ReturnType<typeof vi.fn>
  order: ReturnType<typeof vi.fn>
  limit: ReturnType<typeof vi.fn>
}

vi.mock("@/lib/database", () => ({
  supabase: {
    from: (...args: string[]) => fromSpy(...args),
  },
}))

// Import the functions under test AFTER the mock is declared so the mocked
// Supabase is used by the module.
import { getRecentRequests, getRecentQuotes } from "./pro-actions"

function createChain(result: {
  data?: Record<string, unknown>[] | null
  error?: { message: string } | null
}) {
  // Build a chainable query builder: select -> order -> limit (async resolves)
  builder = {
    select: vi.fn(() => builder),
    order: vi.fn(() => builder),
    limit: vi.fn().mockResolvedValue(result),
  }
  fromSpy = vi.fn(() => builder)
  return builder
}

beforeEach(() => {
  vi.clearAllMocks()
  fromSpy = vi.fn()
  builder = {} as any
})

describe("getRecentRequests", () => {
  it("returns recent requests and calls the correct Supabase chain", async () => {
    const sample: Record<string, string>[] = [{ id: "r1" }, { id: "r2" }]
    createChain({ data: sample, error: null })

    const res = await getRecentRequests()

    expect(res).toEqual(sample)
    expect(fromSpy).toHaveBeenCalledTimes(1)
    expect(fromSpy).toHaveBeenCalledWith("requests")
    expect(builder.select).toHaveBeenCalledWith("*, files (*)")
    expect(builder.order).toHaveBeenCalledWith("created_at", { ascending: false })
    expect(builder.limit).toHaveBeenCalledWith(50)
  })

  it("throws an error when Supabase returns an error", async () => {
    createChain({ data: null, error: { message: "db down" } })

    await expect(getRecentRequests()).rejects.toThrowError(
      "Failed to load requests: db down"
    )
  })

  it("returns an empty array when Supabase returns null/undefined data", async () => {
    createChain({ data: undefined, error: null })

    const res = await getRecentRequests()
    expect(res).toEqual([])
  })
})

describe("getRecentQuotes", () => {
  it("returns recent quotes and calls the correct Supabase chain", async () => {
    const sample: Record<string, string>[] = [{ id: "q1" }, { id: "q2" }]
    createChain({ data: sample, error: null })

    const res = await getRecentQuotes()

    expect(res).toEqual(sample)
    expect(fromSpy).toHaveBeenCalledTimes(1)
    expect(fromSpy).toHaveBeenCalledWith("quotes_with_items")
    expect(builder.select).toHaveBeenCalledWith("*")
    expect(builder.order).toHaveBeenCalledWith("created_at", { ascending: false })
    expect(builder.limit).toHaveBeenCalledWith(20)
  })

  it("throws an error when Supabase returns an error", async () => {
    createChain({ data: null, error: { message: "boom" } })
    await expect(getRecentQuotes()).rejects.toThrowError(
      "Failed to load quotes: boom"
    )
  })
})

describe("getRecentQuotes (null data)", () => {
  it("returns [] when Supabase returns null/undefined data", async () => {
    createChain({ data: undefined, error: null })
    const res = await getRecentQuotes()
    expect(res).toEqual([])
  })
})
