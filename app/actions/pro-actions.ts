"use server"

import { createServerClientForRequest } from "@/lib/database"
import type { DashboardStats, QuoteWithItems, Tables } from "@/lib/types"

type RequestItem = Tables<'requests'>

export async function getRecentRequests(): Promise<RequestItem[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from("requests")
    .select("*, files (*)")
    .order("created_at", { ascending: false })
    .limit(50)

  if (error) {
    throw new Error(`Failed to load requests: ${error.message}`)
  }
  return data || []
}

export async function getRecentQuotes(): Promise<QuoteWithItems[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from("quotes_with_items")
    .select("*")
    .order("created_at", { ascending: false })
    .limit(20)

  if (error) {
    throw new Error(`Failed to load quotes: ${error.message}`)
  }
  return data || []
}


export async function getRecentRequestsForUser(userId: string): Promise<RequestItem[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from("requests")
    .select("*, files (*)")
    .eq("assigned_to", userId)
    .order("created_at", { ascending: false })
    .limit(50)

  if (error) {
    throw new Error(`Failed to load requests for user: ${error.message}`)
  }
  return data || []
}

export async function getRecentQuotesForUser(userId: string): Promise<QuoteWithItems[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from("quotes_with_items")
    .select("*")
    .eq("created_by", userId)
    .order("created_at", { ascending: false })
    .limit(20)

  if (error) {
    throw new Error(`Failed to load quotes for user: ${error.message}`)
  }
  return data || []
}

export async function getDashboardStatsAction(): Promise<DashboardStats> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('dashboard_stats')
    .select('*')
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      return { pending_requests: 0, analyzing_requests: 0, draft_quotes: 0, sent_quotes: 0, accepted_quotes: 0, total_accepted_amount: 0, avg_quote_amount: 0 }
    }
    throw new Error(`Failed to get dashboard stats: ${error.message}`)
  }
  return data
}
