"use server"

import { revalidatePath } from "next/cache"
import { createServerClientForRequest } from "@/lib/database"
import { assignRequestToPro as assignRequestToProDB } from "@/lib/database"
import { analyzeRequest } from "./analyze"

 import { convertLegacyQuoteToDatabase, generateBaseQuoteItems } from "@/lib/quote-utils"
import type { ClientInput, QuoteWithItems, RequestItem } from "@/lib/types"

export interface ProcessRequestPayload {
  input: ClientInput
  clientName?: string
  clientEmail?: string
  files?: { name: string; type: string; url: string; size: number }[]
}

export interface ProcessRequestResult {
  success: boolean
  error?: string
  data?: {
    request: RequestItem
    quote?: QuoteWithItems
    aiSummary?: string
  }
}

export async function processClientRequest(payload: ProcessRequestPayload): Promise<ProcessRequestResult> {
  try {
    const supabase = await createServerClientForRequest()

    // Generate request ID
    const requestId = `req_${Date.now()}_${Math.random().toString(36).slice(2, 8)}`

    // Create request record (ai_summary updated after analysis)
    const requestData = {
      id: requestId,
      status: 'en_attente',
      priority: payload.input.urgence === 'haute'
        ? 1
        : payload.input.urgence === 'basse'
          ? 3
          : 2,
      client_name: payload.clientName,
      client_email: payload.clientEmail,
      input: payload.input,
    }

    const { data: request, error: requestError } = await supabase
      .from('requests')
      .insert([requestData])
      .select()
      .single()

    if (requestError) {
      throw new Error(`Failed to create request: ${requestError.message}`)
    }

    // Analyze with AI (now the FK exists) and persist quote
    const analysisResult = await analyzeRequest({
      input: payload.input,
      requestId,
      userId: undefined,
      persist: true
    })

    // Update ai_summary on the request
    await supabase
      .from('requests')
      .update({ ai_summary: analysisResult.aiSummary })
      .eq('id', requestId)

    // Create files records if any
    if (payload.files && payload.files.length > 0) {
      const filesData = payload.files.map(file => ({
        request_id: requestId,
        name: file.name,
        type: file.type,
        size: file.size,
        url: file.url
      }))

      const { error: filesError } = await supabase
        .from('files')
        .insert(filesData)

      if (filesError) {
        console.error('Failed to create files:', filesError)
        // Attempt to rollback the previously created request to avoid an inconsistent state
        try {
          const { error: deleteError } = await supabase
            .from('requests')
            .delete()
            .eq('id', requestId)

          if (deleteError) {
            console.error('Failed to rollback request after files insert failure:', deleteError)
          } else {
            console.info('Rolled back request due to files insert failure:', requestId)
          }
        } catch (delErr) {
          console.error('Unexpected error during rollback of request:', delErr)
        }

        // Throw so outer catch returns an error result instead of continuing as success
        throw new Error(`Failed to create files: ${filesError.message || filesError}`)
      }
    }

    // Create a professional quote in the new database structure
    // This will be used by professionals for editing
    let databaseQuote: QuoteWithItems | undefined

    if (analysisResult.databaseQuote) {
      databaseQuote = analysisResult.databaseQuote
    } else {
      try {
        // Convert legacy quote to new format and create in database
        const { quote: quoteData, items: itemsData } = convertLegacyQuoteToDatabase(
          analysisResult.quote,
          requestId,
          'system' // System-generated quote
        )

        // Create quote
        const { data: newQuote, error: quoteError } = await supabase
          .from('quotes')
          .insert([quoteData])
          .select()
          .single()

        if (quoteError) {
          console.error('Failed to create quote:', quoteError)
        } else {
          // Create quote items
          const itemsWithQuoteId = itemsData.map(item => ({
            ...item,
            quote_id: newQuote.id
          }))

          const { error: itemsError } = await supabase
            .from('quote_items')
            .insert(itemsWithQuoteId)

          if (itemsError) {
            console.error('Failed to create quote items:', itemsError)
          } else {
            // Fetch complete quote with items
            const { data: completeQuote } = await supabase
              .from('quotes_with_items')
              .select('*')
              .eq('id', newQuote.id)
              .single()

            if (completeQuote) {
              databaseQuote = completeQuote as QuoteWithItems
            }
          }
        }
      } catch (error) {
        console.error('Error creating database quote:', error)
        // Continue without database quote - legacy format will still work
      }
    }

    // Update request status to analyzed
    await supabase
      .from('requests')
      .update({ status: 'en_analyse' })
      .eq('id', requestId)

    revalidatePath('/pro/dashboard')

    return {
      success: true,
      data: {
        request,
        quote: databaseQuote,
        aiSummary: analysisResult.aiSummary
      }
    }

  } catch (error) {
    console.error('Error processing client request:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error occurred'
    }
  }
}

export async function updateRequestStatus(
  requestId: string,
  status: string
): Promise<{ success: boolean; error?: string }> {
  try {
    const supabase = await createServerClientForRequest()
    const updates: { status: string; updated_at: string } = {
      status,
      updated_at: new Date().toISOString()
    }

    const { error } = await supabase
      .from('requests')
      .update(updates)
      .eq('id', requestId)

    if (error) {
      throw new Error(`Failed to update request: ${error.message}`)
    }

    revalidatePath('/pro/dashboard')
    revalidatePath(`/pro/quote-editor/${requestId}`)

    return { success: true }
  } catch (error) {
    console.error('Error updating request status:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function getRequestById(requestId: string) {
  try {
    const supabase = await createServerClientForRequest()
    const { data: request, error } = await supabase
      .from('requests')
      .select(`
        *,
        files (*)
      `)
      .eq('id', requestId)
      .single()

    if (error) {
      throw new Error(`Failed to get request: ${error.message}`)
    }

    return { success: true, data: request }
  } catch (error) {
    console.error('Error getting request:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function getRequestQuotes(requestId: string) {
  try {
    const supabase = await createServerClientForRequest()
    const { data: quotes, error } = await supabase
      .from('quotes_with_items')
      .select('*')
      .eq('request_id', requestId)
      .order('version', { ascending: false })

    if (error) {
      throw new Error(`Failed to get request quotes: ${error.message}`)
    }

    return { success: true, data: quotes || [] }
  } catch (error) {
    console.error('Error getting request quotes:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function assignRequestToPro(
  requestId: string,
  proUserId: string
): Promise<{ success: boolean; error?: string }> {
  try {
    // Use service role to assign request (bypasses RLS for assigned_to field)
    await assignRequestToProDB(requestId, proUserId)

    // Also update any existing quotes to be owned by this pro (using server client for regular operations)
    const supabase = await createServerClientForRequest()
    const { error: quotesError } = await supabase
      .from('quotes')
      .update({
        created_by: proUserId,
        modified_by: proUserId,
        updated_at: new Date().toISOString()
      })
      .eq('request_id', requestId)

    if (quotesError) {
      console.error('Failed to update quote ownership:', quotesError)
    }

    revalidatePath('/pro/dashboard')
    revalidatePath(`/pro/quote-editor/${requestId}`)

    return { success: true }
  } catch (error) {
    console.error('Error assigning request:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}

export async function createQuoteForRequest(
  requestId: string,
  userId: string,
  templateId?: string
): Promise<{ success: boolean; error?: string; data?: QuoteWithItems }> {
  try {
    const supabase = await createServerClientForRequest()
  let quote: QuoteWithItems | undefined

    if (templateId) {
      // Create from template
      const { createQuoteFromTemplateAction } = await import('./quote-pro')
      const result = await createQuoteFromTemplateAction({
        requestId,
        templateId
      })

      if (!result.success) {
        throw new Error(result.error)
      }

if (!result.data) {
  throw new Error('Les données du devis sont manquantes après la création à partir du template.');
}
quote = result.data;
    } else {
      // Create from request input
      const requestResult = await getRequestById(requestId)
      if (!requestResult.success) {
        throw new Error(requestResult.error)
      }

      const request = requestResult.data
      const items = generateBaseQuoteItems(request.input)

      // Compute next version
      const { data: latest, error: vErr } = await supabase
        .from('quotes')
        .select('version')
        .eq('request_id', requestId)
        .order('version', { ascending: false })
        .limit(1)
      const nextVersion = vErr || !latest?.length ? 1 : (latest[0].version as number) + 1

      // Create quote
      const { data: newQuote, error: quoteError } = await supabase
        .from('quotes')
        .insert([{
          request_id: requestId,
          created_by: userId,
          modified_by: userId,
          version: nextVersion,
          status: 'brouillon'
        }])
        .select()
        .single()

      if (quoteError) {
        throw new Error(`Failed to create quote: ${quoteError.message}`)
      }

      // Create items
      const itemsWithQuoteId = items.map((item, index) => ({
        ...item,
        quote_id: newQuote.id,
        order_index: index
      }))

      const { error: itemsError } = await supabase
        .from('quote_items')
        .insert(itemsWithQuoteId)

      if (itemsError) {
        throw new Error(`Failed to create quote items: ${itemsError.message}`)
      }

      // Get complete quote
      const { data: completeQuote, error: fetchError } = await supabase
        .from('quotes_with_items')
        .select('*')
        .eq('id', newQuote.id)
        .single()

      if (fetchError) {
        throw new Error(`Failed to fetch complete quote: ${fetchError.message}`)
      }

      if (completeQuote) {
        quote = completeQuote as QuoteWithItems
      }
    }

    // Ensure quote was created
    if (!quote) {
      throw new Error('Failed to create quote')
    }

    // Update request status
    await updateRequestStatus(requestId, 'en_cours')

    revalidatePath('/pro/dashboard')
    revalidatePath(`/pro/quote-editor/${quote.id}`)

    return { success: true, data: quote }
  } catch (error) {
    console.error('Error creating quote for request:', error)
    return {
      success: false,
      error: error instanceof Error ? error.message : 'Unknown error'
    }
  }
}
