"use server"

import { revalidatePath } from "next/cache"
import { cookies } from "next/headers"
import {
  createQuote,
  updateQuote,
  getQuoteWithItems,
  createQuoteItem,
  updateQuoteItem,
  deleteQuoteItem,
  bulkCreateQuoteItems,

  createQuoteFromTemplate,
  scheduleQuoteReminder,
  convertQuoteToInvoice,
  getQuoteHistory,
  createTemplate,
  getTemplatesByUser,
  getTemplatesByCategory,
  createServerClientForRequest
} from "@/lib/database"
import { validateQuote, compareQuoteVersions } from "@/lib/quote-utils"

import type {
  QuoteItemRecord,
  QuoteItemTemplate,
  QuoteHistoryRecord,
  QuoteStatus,
  Category,
  Unit
} from "@/lib/types"

// Helper to get authenticated user
async function getAuthenticatedUser() {
  const supabase = await createServerClientForRequest()

  const { data: { session } } = await supabase.auth.getSession()

  if (!session?.user) {
    throw new Error('User not authenticated')
  }

  const { data: profile } = await supabase
    .from('profiles')
    .select('*')
    .eq('id', session.user.id)
    .single()

  if (!profile || (profile.role !== 'pro' && profile.role !== 'admin')) {
    throw new Error('Insufficient permissions')
  }

  return profile
}

export interface CreateQuoteFromTemplatePayload {
  requestId: string
  templateId: string
  surfaceMultiplier?: number
  customItems?: QuoteItemTemplate[]
}

export interface UpdateQuotePayload {
  quoteId: string
  updates: {
    status?: QuoteStatus
    margin_rate?: number
    tva_rate?: number
    validity_days?: number
    custom_conditions?: string
    internal_comments?: string
  }
}

export interface UpdateQuoteItemPayload {
  itemId: string
  updates: {
    description?: string
    quantity?: number
    unit_price?: number
    material_cost?: number
    labor_cost?: number
    complexity_factor?: number
    is_optional?: boolean
    supplier?: string
    notes?: string
    order_index?: number
  }
}

export interface AddQuoteItemPayload {
  quoteId: string
  item: {
    category: Category
    subcategory?: string
    description: string
    quantity: number
    unit: Unit
    unit_price: number
    material_cost?: number
    labor_cost?: number
    complexity_factor?: number
    is_optional?: boolean
    supplier?: string
    notes?: string
    order_index?: number
  }
}

export interface CreateTemplatePayload {
  name: string
  description?: string
  category: Category
  subcategory?: string
  quoteId: string
  isPublic?: boolean
  tags?: string[]
  notes?: string
}

// === QUOTE MANAGEMENT ===

export async function createQuoteFromTemplateAction(payload: CreateQuoteFromTemplatePayload) {
  try {
    const user = await getAuthenticatedUser()

    const quote = await createQuoteFromTemplate(
      payload.requestId,
      user.id,
      payload.templateId,
      {
        surfaceMultiplier: payload.surfaceMultiplier,
        customItems: payload.customItems
      }
    )

    revalidatePath(`/pro/dashboard`)
    revalidatePath(`/pro/quote-editor/${quote.id}`)

    return { success: true, data: quote }
  } catch (error) {
    console.error('Error creating quote from template:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function updateQuoteAction(payload: UpdateQuotePayload) {
  try {
    const user = await getAuthenticatedUser()

    const existingQuote = await getQuoteWithItems(payload.quoteId)
    if (!existingQuote) {
      throw new Error('Quote not found')
    }

    const updatedQuote = await updateQuote(payload.quoteId, payload.updates, user.id)

    // Si le statut change vers "envoyé", programmer une relance
    if (payload.updates.status === 'envoyé' && existingQuote.status !== 'envoyé') {
      await scheduleQuoteReminder(payload.quoteId, 7, 'relance')
    }

    revalidatePath(`/pro/dashboard`)
    revalidatePath(`/pro/quote-editor/${payload.quoteId}`)

    return { success: true, data: updatedQuote }
  } catch (error) {
    console.error('Error updating quote:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function getQuoteWithItemsAction(quoteId: string) {
  try {
    await getAuthenticatedUser()
    const quote = await getQuoteWithItems(quoteId)
    if (!quote) {
      throw new Error('Quote not found')
    }

    // Valider le devis
    const validation = validateQuote(quote)

    return { success: true, data: { quote, validation } }
  } catch (error) {
    console.error('Error getting quote:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function getQuoteHistoryAction(quoteId: string) {
  try {
    await getAuthenticatedUser()
    const history = await getQuoteHistory(quoteId)
    return { success: true, data: history }
  } catch (error) {
    console.error('Error getting quote history:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// === QUOTE ITEMS MANAGEMENT ===

export async function addQuoteItemAction(payload: AddQuoteItemPayload) {
  try {
    await getAuthenticatedUser()
    const item = await createQuoteItem({
      quote_id: payload.quoteId,
      category: payload.item.category,
      subcategory: payload.item.subcategory,
      description: payload.item.description,
      quantity: payload.item.quantity,
      unit: payload.item.unit,
      unit_price: payload.item.unit_price,
      material_cost: payload.item.material_cost ?? 0,
      labor_cost: payload.item.labor_cost ?? 0,
      complexity_factor: payload.item.complexity_factor ?? 1,
      order_index: payload.item.order_index ?? 0,
      is_optional: payload.item.is_optional ?? false,
      supplier: payload.item.supplier,
      notes: payload.item.notes,
      ai_suggested: false
    })
    // …rest of function…
    revalidatePath(`/pro/quote-editor/${payload.quoteId}`)

    return { success: true, data: item }
  } catch (error) {
    console.error('Error adding quote item:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function updateQuoteItemAction(payload: UpdateQuoteItemPayload) {
  try {
    await getAuthenticatedUser()
    const item = await updateQuoteItem(payload.itemId, payload.updates)

    // Revalidate the quote page to show updated totals
    const quoteId = item.quote_id
    revalidatePath(`/pro/quote-editor/${quoteId}`)

    return { success: true, data: item }
  } catch (error) {
    console.error('Error updating quote item:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function deleteQuoteItemAction(itemId: string, quoteId: string) {
  try {
    await getAuthenticatedUser()
    const quote = await getQuoteWithItems(quoteId)
    if (!quote) throw new Error('Quote not found')
    if (!quote.items?.some(i => i.id === itemId)) {
      throw new Error('Item does not belong to this quote')
    }
    await deleteQuoteItem(itemId)

    revalidatePath(`/pro/quote-editor/${quoteId}`)

    return { success: true }
  } catch (error) {
    console.error('Error deleting quote item:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function reorderQuoteItemsAction(quoteId: string, itemIds: string[]) {
  try {
    await getAuthenticatedUser()
    // Ensure all items belong to the quote
    const all = await getQuoteWithItems(quoteId)
    const allowed = new Set((all?.items ?? []).map(i => i.id))
    if (itemIds.some(id => !allowed.has(id))) {
      throw new Error('One or more items do not belong to this quote')
    }
    // Update order_index for each item
    const updatePromises = itemIds.map((itemId, index) =>
      updateQuoteItem(itemId, { order_index: index })
    )

    await Promise.all(updatePromises)

    revalidatePath(`/pro/quote-editor/${quoteId}`)

    return { success: true }
  } catch (error) {
    console.error('Error reordering quote items:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// === QUOTE ACTIONS ===

export async function sendQuoteToClientAction(quoteId: string) {
  try {
    const user = await getAuthenticatedUser()

    // Valider le devis avant envoi
    const quote = await getQuoteWithItems(quoteId)
    if (!quote) {
      throw new Error('Quote not found')
    }

    const validation = validateQuote(quote)
    if (!validation.isValid) {
      throw new Error(`Cannot send invalid quote: ${validation.errors.join(', ')}`)
    }

    // Mettre à jour le statut et programmer une relance si nécessaire
    if (quote.status !== 'envoyé') {
      await updateQuote(quoteId, { status: 'envoyé' }, user.id)
      await scheduleQuoteReminder(quoteId, 7, 'relance')
    }

    revalidatePath(`/pro/dashboard`)
    revalidatePath(`/pro/quote-editor/${quoteId}`)

    return { success: true, warnings: validation.warnings }
  } catch (error) {
    console.error('Error sending quote:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function duplicateQuoteAction(quoteId: string, newRequestId?: string) {
  try {
    const user = await getAuthenticatedUser()

    const originalQuote = await getQuoteWithItems(quoteId)
    if (!originalQuote) {
      throw new Error('Original quote not found')
    }

    // Créer le nouveau devis
    const newQuote = await createQuote({
      request_id: newRequestId || originalQuote.request_id,
      created_by: user.id,
      ai_generated_data: originalQuote.ai_generated_data
    })

    // Dupliquer les items
    const items = (originalQuote.items || []).map((item, index) => ({
      quote_id: newQuote.id,
      category: item.category,
      subcategory: item.subcategory,
      description: `${item.description} (copie)`,
      quantity: item.quantity,
      unit: item.unit,
      unit_price: item.unit_price,
      material_cost: item.material_cost,
      labor_cost: item.labor_cost,
      complexity_factor: item.complexity_factor,
      order_index: index,
      is_optional: item.is_optional,
      supplier: item.supplier,
      notes: item.notes,
      ai_suggested: false
    }))

    if (items.length > 0) {
      await bulkCreateQuoteItems(items)
    }

    revalidatePath(`/pro/dashboard`)

    const completeNewQuote = await getQuoteWithItems(newQuote.id)

    return { success: true, data: completeNewQuote }
  } catch (error) {
    console.error('Error duplicating quote:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function convertQuoteToInvoiceAction(quoteId: string) {
  try {
    await getAuthenticatedUser()
    const invoiceId = await convertQuoteToInvoice(quoteId)

    revalidatePath(`/pro/dashboard`)
    revalidatePath(`/pro/quote-editor/${quoteId}`)

    return { success: true, data: { invoiceId } }
  } catch (error) {
    console.error('Error converting quote to invoice:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// === TEMPLATES MANAGEMENT ===

export async function createTemplateFromQuoteAction(payload: CreateTemplatePayload) {
  try {
    const user = await getAuthenticatedUser()

    const quote = await getQuoteWithItems(payload.quoteId)
    if (!quote) {
      throw new Error('Quote not found')
    }

    const items: QuoteItemTemplate[] = (quote.items || []).map(item => ({
      category: item.category,
      subcategory: item.subcategory,
      description: item.description,
      quantity: item.quantity,
      unit: item.unit,
      unit_price: item.unit_price,
      material_cost: item.material_cost,
      labor_cost: item.labor_cost,
      complexity_factor: item.complexity_factor,
      is_optional: item.is_optional,
      supplier: item.supplier,
      notes: item.notes
    }))

    const template = await createTemplate({
      name: payload.name,
      description: payload.description,
      category: payload.category,
      subcategory: payload.subcategory,
      items,
      created_by: user.id,
      default_margin: quote.margin_rate,
      is_public: payload.isPublic || false,
      tags: payload.tags,
      notes: payload.notes
    })

    revalidatePath(`/pro/templates`)

    return { success: true, data: template }
  } catch (error) {
    console.error('Error creating template:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function getTemplatesAction(category?: string) {
  try {
    const user = await getAuthenticatedUser()

    const templates = category
      ? await getTemplatesByCategory(category)
      : await getTemplatesByUser()

    return { success: true, data: templates }
  } catch (error) {
    console.error('Error getting templates:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// === QUOTE COMPARISON ===

export async function compareQuoteVersionsAction(quoteId1: string, quoteId2: string) {
  try {
    await getAuthenticatedUser()
    const [quote1, quote2] = await Promise.all([
      getQuoteWithItems(quoteId1),
      getQuoteWithItems(quoteId2)
    ])

    if (!quote1 || !quote2) {
      throw new Error('One or both quotes not found')
    }

    const changes = compareQuoteVersions(quote1, quote2)

    return { success: true, data: changes }
  } catch (error) {
    console.error('Error comparing quotes:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

// === BULK OPERATIONS ===

export async function bulkUpdateQuoteItemsAction(
  quoteId: string,
  updates: { itemId: string; updates: Partial<QuoteItemRecord> }[]
) {
  try {
    await getAuthenticatedUser()
    const quote = await getQuoteWithItems(quoteId)
    if (!quote) throw new Error('Quote not found')
    const allowed = new Set((quote.items ?? []).map(i => i.id))
    if (updates.some(({ itemId }) => !allowed.has(itemId))) {
      throw new Error('One or more items do not belong to this quote')
    }
    const updatePromises = updates.map(({ itemId, updates }) =>
      updateQuoteItem(itemId, updates)
    )

    const results = await Promise.all(updatePromises)

    revalidatePath(`/pro/quote-editor/${quoteId}`)

    return { success: true, data: results }
  } catch (error) {
    console.error('Error bulk updating quote items:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}

export async function applyMarginToAllItemsAction(quoteId: string, marginPercentage: number) {
  try {
    const user = await getAuthenticatedUser()
    await updateQuote(quoteId, { margin_rate: marginPercentage }, user.id)

    revalidatePath(`/pro/quote-editor/${quoteId}`)

    return { success: true }
  } catch (error) {
    console.error('Error applying margin:', error)
    return { success: false, error: error instanceof Error ? error.message : 'Unknown error' }
  }
}
