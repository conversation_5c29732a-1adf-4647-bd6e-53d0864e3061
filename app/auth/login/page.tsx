"use client"

import type React from "react"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { supabase } from "@/lib/supabase-browser"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

export default function LoginPage() {
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState("")
  const router = useRouter()

  const handleLogin = async (e: React.FormEvent) => {
    e.preventDefault()
    setLoading(true)
    setError("")


    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      console.log("Auth result:", { data: !!data, error })

      if (error) {
        console.error("Auth error:", error)
        setError(error.message)
      } else if (data.user) {
        console.log("Login successful, user:", data.user.id)
        console.log("Session:", !!data.session)

            // Check if user has a profile
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('id')
          .eq('id', data.user.id)
          .maybeSingle()

        if (profileError) {
          console.error("Profile check failed:", profileError.message)
          setError("Une erreur est survenue lors de la vérification de votre profil.")
          return
        }

        if (!profile) {
          const { error: createError } = await supabase
            .from('profiles')
            .insert({
              id: data.user.id,
              email: data.user.email,
              name: data.user.user_metadata?.name || data.user.email?.split('@')[0],
              role: 'pro'
            })

          if (createError) {
            console.error("Profile creation failed:", createError.message)
            setError("Une erreur est survenue lors de la création de votre profil.")
            return
          }
        }

        router.push("/pro/dashboard")
      } else {
        console.error("No error but no user data either")
        setError("Échec de la connexion - aucun utilisateur retourné")
      }
    } catch {
      setError("Une erreur est survenue lors de la connexion")
    } finally {
      setLoading(false)
    }
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-indigo-100 p-4">
      <Card className="w-full max-w-md">
        <CardHeader className="space-y-1">
          <CardTitle className="text-2xl font-bold text-center">Connexion BTP Pro</CardTitle>
          <CardDescription className="text-center">Accédez à votre espace professionnel de devis BTP</CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleLogin} className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input
                id="email"
                type="email"
                placeholder="<EMAIL>"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                required
              />
            </div>

            <div className="space-y-2">
              <Label htmlFor="password">Mot de passe</Label>
              <Input
                id="password"
                type="password"
                placeholder="••••••••"
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                required
              />
            </div>

            <Button type="submit" className="w-full" disabled={loading}>
              {loading ? "Connexion..." : "Se connecter"}
            </Button>
          </form>

          <div className="mt-4 text-center text-sm text-gray-600">
            <p>Compte de test :</p>
            <p>Email: <EMAIL></p>
            <p>Mot de passe: demo123</p>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
