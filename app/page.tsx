"use client"

import type React from "react"

import { useState, useEffect } from "react"
import Link from "next/link"
import { useRout<PERSON> } from "next/navigation"
import { analyzeRequest } from "./actions/analyze"
import type { ClientInput, Category, RequestRecord } from "@/lib/types"
import FileDropzone from "@/components/file-dropzone"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useRequests } from "@/hooks/use-local-store"
import { Loader2 } from "lucide-react"

// Helper function to convert File[] to {url, name}[] using readAsDataURL
const convertFilesToDataURLs = async (files: File[]): Promise<{ url: string; name: string }[]> => {
  const results: { url: string; name: string }[] = [];

  for (const file of files) {
    try {
      const dataURL = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = () => reject(new Error(`Failed to read file: ${file.name}`));
        reader.readAsDataURL(file);
      });

      results.push({ url: dataURL, name: file.name });
    } catch (error) {
      console.error(`Error converting file ${file.name} to data URL:`, error);
      // Fallback to a placeholder or skip the file
      results.push({ url: "", name: file.name });
    }
  }

  return results;
};

export default function Page() {
  const router = useRouter()
  const { addRequest } = useRequests()
  const [files, setFiles] = useState<File[]>([])
  const [pending, setPending] = useState(false)
  const [form, setForm] = useState<ClientInput & { localisation: string; budgetIndicatif?: number }>({
    description: "",
    typeRenovation: "autre",
    urgence: "normale",
    localisation: "",
    surfaceM2: undefined,
    budgetIndicatif: undefined,
    files: [],
  })

  // Cleanup any remaining object URLs on unmount
  useEffect(() => {
    return () => {
      // This is a safety cleanup in case any object URLs were created elsewhere
      // The main cleanup is handled in the FileDropzone component
    }
  }, [])

  const onSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    setPending(true)
    try {
      const input: ClientInput = {
        description: form.description,
        typeRenovation: form.typeRenovation,
        urgence: form.urgence,
        surfaceM2: form.surfaceM2,
        files: files,
      }
      const { quote, aiSummary, usedAIProvider } = await analyzeRequest({ input })

      // Convert files to durable data URLs
      const fileDataURLs = await convertFilesToDataURLs(files)

      const now = new Date().toISOString()
      const record: RequestRecord = {
        id: `r_${crypto.randomUUID()}`,
        createdAt: now,
        updatedAt: now,
        status: "en_attente" as const,
        priority: form.urgence === "haute" ? 3 : form.urgence === "normale" ? 2 : 1,
        client: {},
        input: { ...input, localisation: form.localisation, budgetIndicatif: form.budgetIndicatif },
        previews: (() => {
          const images: { url: string; name: string }[] = [];
          const plans: { url: string; name: string }[] = [];
          for (const fileData of fileDataURLs) {
            const file = files.find(f => f.name === fileData.name);
            if (file && file.type.startsWith("image/")) {
              images.push({ url: fileData.url, name: fileData.name });
            } else if (file) {
              plans.push({ url: fileData.url, name: fileData.name });
            }
          }
          return { images, plans };
        })(),
        aiSummary: `${aiSummary} ${usedAIProvider === "gemini" ? "(Gemini)" : "(Règles)"}`,
        quote,
      }
      addRequest(record)
      router.push("/pro/dashboard")
    } finally {
      setPending(false)
    }
  }

  return (
    <main className="min-h-[100dvh]">
      <header className="border-b">
        <div className="max-w-6xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="h-8 w-8 rounded bg-gray-900" aria-hidden />
            <span className="font-semibold">Devis Rénovation IA</span>
            <Badge variant="secondary">MVP</Badge>
          </div>
          <nav className="flex items-center gap-3 text-sm">
            <Link href="/pro/dashboard" className="hover:underline">
              Espace professionnel
            </Link>
          </nav>
        </div>
      </header>

      <section className="max-w-5xl mx-auto px-4 py-8 md:py-12">
        <div className="grid lg:grid-cols-2 gap-6">
          <Card>
            <CardHeader>
              <CardTitle>Soumettre une demande</CardTitle>
            </CardHeader>
            <CardContent>
              <form className="space-y-6" onSubmit={onSubmit}>
                <div className="space-y-2">
                  <Label htmlFor="description">Description détaillée</Label>
                  <Textarea
                    id="description"
                    required
                    placeholder={
                      "Décrivez votre besoin (ex: refaire l'électricité du salon, 20m², ajout de 6 prises...)"
                    }
                    value={form.description}
                    onChange={(e) => setForm((f) => ({ ...f, description: e.target.value }))}
                    rows={6}
                  />
                </div>

                <div className="grid md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Type de rénovation</Label>
                    <Select
                      value={form.typeRenovation}
                      onValueChange={(v) => setForm((f) => ({ ...f, typeRenovation: v as Category }))}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Catégorie" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="électricité">Électricité</SelectItem>
                        <SelectItem value="plomberie">Plomberie</SelectItem>
                        <SelectItem value="maçonnerie">Maçonnerie</SelectItem>
                        <SelectItem value="peinture">Peinture</SelectItem>
                        <SelectItem value="menuiserie">Menuiserie</SelectItem>
                        <SelectItem value="isolation">Isolation</SelectItem>
                        <SelectItem value="démolition">Démolition</SelectItem>
                        <SelectItem value="autre">Autre</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label>Urgence</Label>
                    <Select value={form.urgence} onValueChange={(v) => setForm((f) => ({ ...f, urgence: v as "basse" | "normale" | "haute" }))}>
                      <SelectTrigger>
                        <SelectValue placeholder="Urgence" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="basse">Basse</SelectItem>
                        <SelectItem value="normale">Normale</SelectItem>
                        <SelectItem value="haute">Haute</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="localisation">Localisation</Label>
                    <Input
                      id="localisation"
                      placeholder="Ville / Code postal"
                      value={form.localisation}
                      onChange={(e) => setForm((f) => ({ ...f, localisation: e.target.value }))}
                      required
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="surface">Surface approximative (m²)</Label>
                    <Input
                      id="surface"
                      type="number"
                      min={0}
                      step="0.5"
                      placeholder="ex: 25"
                      value={form.surfaceM2 ?? ""}
                      onChange={(e) =>
                        setForm((f) => ({ ...f, surfaceM2: e.target.value ? Number(e.target.value) : undefined }))
                      }
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="budget">Budget indicatif (€)</Label>
                    <Input
                      id="budget"
                      type="number"
                      min={0}
                      step="50"
                      placeholder="ex: 3000"
                      value={form.budgetIndicatif ?? ""}
                      onChange={(e) =>
                        setForm((f) => ({ ...f, budgetIndicatif: e.target.value ? Number(e.target.value) : undefined }))
                      }
                    />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label>Fichiers (photos, plans, PDF)</Label>
                  <FileDropzone onFilesChange={setFiles} />
                </div>

                <div className="flex justify-end">
                  <Button type="submit" disabled={pending}>
                    {pending && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    Analyser et générer un devis
                  </Button>
                </div>
              </form>
            </CardContent>
          </Card>

          <Card className="bg-muted/40">
            <CardHeader>
              <CardTitle>Comment ça marche</CardTitle>
            </CardHeader>
            <CardContent className="text-sm space-y-4 leading-relaxed">
              <p>
                1) Vous décrivez votre besoin et déposez vos photos/plans. 2) L’IA analyse votre demande et génère un
                devis initial structuré. 3) Un professionnel valide et ajuste avant envoi. 4) Vous recevez un devis
                détaillé par email.
              </p>
              <div className="rounded-md border p-3">
                <div className="font-medium mb-1">Précision attendue</div>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Estimation IA à ±20% dans 80% des cas</li>
                  <li>Reconnaissance des éléments photo &gt; 90% (prochaine version)</li>
                  <li>Calcul des surfaces plan ±5% (prochaine version)</li>
                </ul>
              </div>
              <div className="rounded-md border p-3">
                <div className="font-medium mb-1">Conformité & sécurité</div>
                <ul className="list-disc pl-5 space-y-1 text-muted-foreground">
                  <li>Données chiffrées en transit</li>
                  <li>Contrôle et validation humaine obligatoire</li>
                  <li>Traçabilité des modifications (éditeur pro)</li>
                </ul>
              </div>
              <div className="rounded-md border p-3">
                <div className="font-medium mb-1">Espace professionnel</div>
                <p className="text-muted-foreground">
                  Accédez à la file des demandes, validez et envoyez les devis, convertissez en factures.
                </p>
                <div className="mt-2">
                  <Link href="/pro/dashboard" className="text-sm underline">
                    Ouvrir le dashboard pro
                  </Link>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </section>
    </main>
  )
}
