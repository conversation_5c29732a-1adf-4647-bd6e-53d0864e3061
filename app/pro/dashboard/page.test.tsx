/**
 * Tests for ProDashboardPage
 *
 * Testing library/framework: React Testing Library with the project's configured runner (Jest/Vitest).
 * - We use @testing-library/react for rendering and querying.
 * - We use @testing-library/user-event for interactions.
 * - We mock next/navigation, auth hook, and server actions.
 *
 * Coverage focus (from diff):
 * - Loading state and error rendering
 * - Stats rendering (including number formatting)
 * - Requests table filtering (search, status, priority)
 * - Assign request flow (success & failure)
 * - Create quote flow (dialog interactions, template selection optional, navigation)
 * - Quotes list actions: edit navigation and send flow (success & failure)
 * - Templates grid navigation
 */

import React from 'react'
import { render, screen, within, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import { describe, it, expect, beforeEach, vi } from 'vitest'
import '@testing-library/jest-dom'

// Mocks: adjust module specifiers if repository uses different paths.
// We defer-resolve these specifiers via jest/vitest automock.
// If your repo uses path aliases (e.g., @/lib/...), update the strings below to match actual imports.
vi.mock('next/navigation', () => {
  const push = vi.fn()
  return {
    useRouter: () => ({ push }),
    // expose push for assertions
    __esModule: true,
    __mock: { push },
  }
})

// Try common paths for the auth hook and actions. Update if your repo differs.
vi.mock('@/lib/hooks/useProAuth', () => ({
  useProAuth: vi.fn(),
}))

vi.mock('@/lib/actions/pro/dashboard', () => ({
  getDashboardStats: vi.fn(),
  getRecentRequests: vi.fn(),
  getRecentQuotes: vi.fn(),
}))

vi.mock('@/lib/actions/pro/templates', () => ({
  getTemplatesAction: vi.fn(),
}))

vi.mock('@/lib/actions/pro/requests', () => ({
  assignRequestToPro: vi.fn(),
}))

vi.mock('@/lib/actions/pro/quotes', () => ({
  createQuoteForRequest: vi.fn(),
  sendQuoteToClientAction: vi.fn(),
}))

// Import the page under test after mocks are set up
import ProDashboardPage from './page'

import { useProAuth } from '@/hooks/use-auth'
import { getDashboardStats } from '@/lib/database'
import { getRecentRequests, getRecentQuotes } from 'app/actions/pro-actions'
import { assignRequestToPro, createQuoteForRequest } from 'app/process-request'
import { getTemplatesAction, sendQuoteToClientAction } from 'app/quote-pro'
import { useRouter } from 'next/navigation'
// Helpers
const mockUser = { id: 'pro_123', name: 'Jean Pro' }

const makeRequest = (overrides: Partial<any> = {}) => ({
  id: `req_${Math.random().toString(36).slice(2, 8)}`,
  client_name: 'Client Exemple',
  client_email: '<EMAIL>',
  ai_summary: 'Résumé AI pour des travaux de rénovation de salle de bain.',
  input: { typeRenovation: 'Salle de bain' },
  status: 'en_attente',
  priority: 2,
  created_at: new Date('2025-01-15T10:00:00Z').toISOString(),
  ...overrides,
})

const makeQuote = (overrides: Partial<any> = {}) => ({
  id: `quo_${Math.random().toString(36).slice(2, 8)}`,
  client_name: 'Client Exemple',
  client_email: '<EMAIL>',
  total_ttc: 12345.67,
  status: 'validé',
  version: 3,
  created_at: new Date('2025-02-01T09:00:00Z').toISOString(),
  items: [],
  ...overrides,
})

const makeTemplate = (overrides: Partial<any> = {}) => ({
  id: `tpl_${Math.random().toString(36).slice(2, 8)}`,
  name: 'Rénovation Cuisine',
  category: 'Cuisine',
  description: 'Template standard cuisine',
  items: [],
  usage_count: 5,
  ...overrides,
})

const setupHappyPathMocks = ({
  requests = [makeRequest(), makeRequest({ status: 'en_cours', priority: 1 })],
  quotes = [makeQuote(), makeQuote({ status: 'envoyé', version: 1 })],
  stats = {
    pending_requests: 4,
    sent_quotes: 7,
    accepted_quotes: 2,
    total_accepted_amount: 98765,
  },
  templates = [makeTemplate(), makeTemplate({ category: 'Salle de bain', name: 'Salle de bain rapide' })],
} = {}) => {
  ;(useProAuth as vi.Mock).mockReturnValue({ user: mockUser, loading: false })
  ;(getDashboardStats as vi.Mock).mockResolvedValue(stats)
  ;(getRecentRequests as vi.Mock).mockResolvedValue(requests)
  ;(getRecentQuotes as vi.Mock).mockResolvedValue(quotes)
  ;(getTemplatesAction as vi.Mock).mockResolvedValue({ success: true, data: templates })
  ;(assignRequestToPro as vi.Mock).mockResolvedValue({ success: true })
  ;(createQuoteForRequest as vi.Mock).mockResolvedValue({ success: true, data: { id: 'q_abc123' } })
  ;(sendQuoteToClientAction as vi.Mock).mockResolvedValue({ success: true })
}

const renderPage = () => render(<ProDashboardPage />)

describe('ProDashboardPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  it('shows loading indicator while auth or data is loading', async () => {
    ;(useProAuth as vi.Mock).mockReturnValue({ user: mockUser, loading: false })
    // Make first data call pending to see loading state at initial render
    let resolveStats: (v: any) => void
    const statsPromise = new Promise((res) => { resolveStats = res as any })
    ;(getDashboardStats as vi.Mock).mockReturnValue(statsPromise)
    ;(getRecentRequests as vi.Mock).mockResolvedValue([])
    ;(getRecentQuotes as vi.Mock).mockResolvedValue([])
    ;(getTemplatesAction as vi.Mock).mockResolvedValue({ success: true, data: [] })

    renderPage()

    // Loading text present
    expect(screen.getByText('Chargement du tableau de bord...')).toBeInTheDocument()

    // Resolve and wait for transition
    resolveStats!({ pending_requests: 0, sent_quotes: 0, accepted_quotes: 0, total_accepted_amount: 0 })
    await waitFor(() =>
      expect(screen.queryByText('Chargement du tableau de bord...')).not.toBeInTheDocument()
    )
  })

  it('renders error alert when loadDashboardData throws', async () => {
    ;(useProAuth as vi.Mock).mockReturnValue({ user: mockUser, loading: false })
    ;(getDashboardStats as vi.Mock).mockRejectedValue(new Error('Boom'))
    ;(getRecentRequests as vi.Mock).mockResolvedValue([])
    ;(getRecentQuotes as vi.Mock).mockResolvedValue([])
    ;(getTemplatesAction as vi.Mock).mockResolvedValue({ success: true, data: [] })

    renderPage()

    expect(await screen.findByText('Erreur')).toBeInTheDocument()
    expect(screen.getByText('Boom')).toBeInTheDocument()
  })

  it('renders stats cards when stats loaded', async () => {
    setupHappyPathMocks({
      stats: { pending_requests: 5, sent_quotes: 9, accepted_quotes: 3, total_accepted_amount: 1234567 },
    })
    renderPage()

    // Wait for dashboard to load
    expect(await screen.findByText('Tableau de bord')).toBeInTheDocument()

    // Stats
    expect(screen.getByText('Demandes en attente')).toBeInTheDocument()
    expect(screen.getByText('5')).toBeInTheDocument()
    expect(screen.getByText('Devis envoyés')).toBeInTheDocument()
    expect(screen.getByText('9')).toBeInTheDocument()
    expect(screen.getByText('Devis acceptés')).toBeInTheDocument()
    expect(screen.getByText('3')).toBeInTheDocument()
    // Formatted amount (fr-FR): "1 234 567 €" (thin space / NBSP variations)
    expect(screen.getByText(/1(?:\s| )?234(?:\s| )?567\s€/)).toBeInTheDocument()
  })

  it('filters requests by search, status, and priority', async () => {
    const r1 = makeRequest({ client_name: 'Alice', client_email: '<EMAIL>', status: 'en_attente', priority: 1 })
    const r2 = makeRequest({ client_name: 'Bob', client_email: '<EMAIL>', status: 'en_cours', priority: 3 })
    const r3 = makeRequest({ client_name: 'Charlie', client_email: '<EMAIL>', status: 'refusé', priority: 2 })
    setupHappyPathMocks({ requests: [r1, r2, r3] })

    renderPage()
    // Wait for table
    const table = await screen.findByRole('table')

    // Initially all 3 visible
    expect(within(table).getByText('Alice')).toBeInTheDocument()
    expect(within(table).getByText('Bob')).toBeInTheDocument()
    expect(within(table).getByText('Charlie')).toBeInTheDocument()

    const user = userEvent.setup()

    // Search "bob" reduces to Bob
    await user.type(screen.getByPlaceholderText('Rechercher par client ou description...'), 'bob')
    await waitFor(() => {
      expect(within(table).queryByText('Alice')).not.toBeInTheDocument()
      expect(within(table).getByText('Bob')).toBeInTheDocument()
      expect(within(table).queryByText('Charlie')).not.toBeInTheDocument()
    })

    // Clear search
    await user.clear(screen.getByPlaceholderText('Rechercher par client ou description...'))

    // Filter status = "refusé"
    await user.click(screen.getByRole('button', { name: 'Statut' })) // SelectTrigger
    await user.click(screen.getByRole('option', { name: 'Refusé' }))
    await waitFor(() => {
      expect(within(table).queryByText('Alice')).not.toBeInTheDocument()
      expect(within(table).queryByText('Bob')).not.toBeInTheDocument()
      expect(within(table).getByText('Charlie')).toBeInTheDocument()
    })

    // Filter priority = "Basse" (3)
    await user.click(screen.getByRole('button', { name: 'Priorité' }))
    await user.click(screen.getByRole('option', { name: 'Basse' }))
    // Now none match (refusé + basse), should yield empty tbody
    await waitFor(() => {
      expect(within(table).queryByText('Charlie')).not.toBeInTheDocument()
    })
  })

  it('shows "Prendre" button for en_attente and calls assign then refreshes', async () => {
    const rPending = makeRequest({ id: 'req_pending', status: 'en_attente' })
    setupHappyPathMocks({ requests: [rPending] })
    renderPage()

    const user = userEvent.setup()
    const takeBtn = await screen.findByRole('button', { name: /Prendre/ })
    await user.click(takeBtn)

    expect(assignRequestToPro).toHaveBeenCalledWith('req_pending', mockUser.id)
    // After success, loadDashboardData is called again; assert refresh by ensuring actions called more than once
    await waitFor(() => {
      expect(getDashboardStats).toHaveBeenCalledTimes(2)
      expect(getRecentRequests).toHaveBeenCalledTimes(2)
      expect(getRecentQuotes).toHaveBeenCalledTimes(2)
    })
  })

  it('assign handles failure and shows error alert', async () => {
    const rPending = makeRequest({ id: 'req_fail', status: 'en_attente' })
    setupHappyPathMocks({ requests: [rPending] })
    ;(assignRequestToPro as vi.Mock).mockResolvedValueOnce({ success: false, error: 'Assign error' })

    renderPage()
    const user = userEvent.setup()
    await user.click(await screen.findByRole('button', { name: /Prendre/ }))

    // Error screen
    expect(await screen.findByText('Erreur')).toBeInTheDocument()
    expect(screen.getByText('Assign error')).toBeInTheDocument()
  })

  it('create quote from dialog navigates to editor on success', async () => {
    const r = makeRequest({ id: 'req_create', status: 'en_cours' })
    setupHappyPathMocks({ requests: [r], templates: [makeTemplate({ id: 'tpl1', name: 'T1', category: 'Cat' })] })

    renderPage()
    const user = userEvent.setup()

    // Open dialog
    await user.click(await screen.findByRole('button', { name: /Devis/ }))

    // Click "Créer le devis" without selecting template (should pass undefined)
    await user.click(await screen.findByRole('button', { name: /Créer le devis/ }))

    expect(createQuoteForRequest).toHaveBeenCalledWith('req_create', mockUser.id, undefined)

    // Navigation
    const { __mock } = useRouter() as any
    await waitFor(() => {
      expect(__mock.push).toHaveBeenCalledWith('/pro/quote-editor/q_abc123')
    })
  })

  it('create quote failure shows error alert', async () => {
    const r = makeRequest({ id: 'req_create_fail' })
    setupHappyPathMocks({ requests: [r] })
    ;(createQuoteForRequest as vi.Mock).mockResolvedValueOnce({ success: false, error: 'Create failed' })

    renderPage()
    const user = userEvent.setup()
    await user.click(await screen.findByRole('button', { name: /Devis/ }))
    await user.click(await screen.findByRole('button', { name: /Créer le devis/ }))

    expect(await screen.findByText('Erreur')).toBeInTheDocument()
    expect(screen.getByText('Create failed')).toBeInTheDocument()
  })

  it('quotes table allows editing and sending when status is validé', async () => {
    const qValid = makeQuote({ id: 'quo_valid', status: 'validé' })
    setupHappyPathMocks({ quotes: [qValid] })

    renderPage()
    const user = userEvent.setup()

    // Edit navigates
    await user.click(await screen.findByRole('button', { name: /Modifier/ }))
    const { __mock } = useRouter() as any
    expect(__mock.push).toHaveBeenCalledWith('/pro/quote-editor/quo_valid')

    // Send calls action and refresh
    await user.click(await screen.findByRole('button', { name: /Envoyer/ }))
    expect(sendQuoteToClientAction).toHaveBeenCalledWith('quo_valid')

    await waitFor(() => {
      expect(getDashboardStats).toHaveBeenCalledTimes(2)
      expect(getRecentQuotes).toHaveBeenCalledTimes(2)
    })
  })

  it('send quote failure surfaces error', async () => {
    const qValid = makeQuote({ id: 'quo_error', status: 'validé' })
    setupHappyPathMocks({ quotes: [qValid] })
    ;(sendQuoteToClientAction as vi.Mock).mockResolvedValueOnce({ success: false, error: 'Send failed' })

    renderPage()
    const user = userEvent.setup()
    await user.click(await screen.findByRole('button', { name: /Envoyer/ }))

    expect(await screen.findByText('Erreur')).toBeInTheDocument()
    expect(screen.getByText('Send failed')).toBeInTheDocument()
  })

  it('templates grid navigation works', async () => {
    const tpl = makeTemplate({ id: 'tpl_42', name: 'T42' })
    setupHappyPathMocks({ templates: [tpl] })

    renderPage()
    const user = userEvent.setup()

    await user.click(await screen.findByRole('button', { name: /Voir/ }))
    const { __mock } = useRouter() as any
    expect(__mock.push).toHaveBeenCalledWith('/pro/templates/tpl_42')
  })
})
