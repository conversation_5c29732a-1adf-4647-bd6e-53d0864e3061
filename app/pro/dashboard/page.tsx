"use client"

import { useEffect, use<PERSON><PERSON><PERSON>, useState, use<PERSON><PERSON>back } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"

import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import {
  Plus,
  Search,
  Eye,
  Edit,
  Send,
  CheckCircle2,
  <PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>
} from "lucide-react"
import { getRecentRequestsForUser, getRecentQuotesForUser, getDashboardStatsAction } from "@/app/actions/pro-actions"
import {
  assignRequestToPro,
  createQuoteForRequest
} from "@/app/actions/process-request"
import {
  sendQuoteToClientAction,
  getTemplatesAction
} from "@/app/actions/quote-pro"
import { useProAuth } from "@/hooks/use-auth"
import type { DashboardStats, QuoteWithItems, TemplateRecord, RequestItem } from "@/lib/types"

// Quotes returned from `quotes_with_items` may include denormalized client fields.
type QuoteRow = QuoteWithItems & {
  client_name?: string
  client_email?: string
}

export default function ProDashboardPage() {
  const router = useRouter()
  const { user, loading: authLoading } = useProAuth()
  const [stats, setStats] = useState<DashboardStats | null>(null)
  const [requests, setRequests] = useState<RequestItem[]>([])
  const [quotes, setQuotes] = useState<QuoteRow[]>([])
  const [templates, setTemplates] = useState<TemplateRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Filters and search
  const [searchTerm, setSearchTerm] = useState("")
  const [statusFilter, setStatusFilter] = useState("all")
  const [priorityFilter, setPriorityFilter] = useState("all")

  // Dialog states
  const [showCreateQuote, setShowCreateQuote] = useState<string | null>(null)
  const [selectedTemplate, setSelectedTemplate] = useState("")
  const [createQuoteInProgress, setCreateQuoteInProgress] = useState<Set<string>>(new Set())
  const [sendQuoteInProgress, setSendQuoteInProgress] = useState<Set<string>>(new Set())
  const [assignRequestInProgress, setAssignRequestInProgress] = useState<Set<string>>(new Set())

  const loadDashboardData = useCallback(async () => {
    setLoading(true)
    setError(null)
    try {
      const [statsResult, requestsData, quotesData] = await Promise.all([
        getDashboardStatsAction(),
        getRecentRequestsForUser(user!.id),
        getRecentQuotesForUser(user!.id),
      ])
      setStats(statsResult)
      setRequests(requestsData)
      setQuotes(quotesData)

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Erreur lors du chargement')
    } finally {
      setLoading(false)
    }
  }, [user])

  const loadTemplates = useCallback(async () => {
    try {
      const result = await getTemplatesAction()
      if (result.success) {
        setTemplates(result.data || [])
      }
    } catch (err) {
      console.error('Failed to load templates:', err)
    }
  }, [])

  useEffect(() => {
    if (user) {
      loadDashboardData()
      loadTemplates()
    }
  }, [user, loadDashboardData, loadTemplates])

  const handleAssignRequest = async (requestId: string) => {
    if (!user || assignRequestInProgress.has(requestId)) return

    // Add to in-progress set
    setAssignRequestInProgress(prev => new Set(prev).add(requestId))

    try {
      const result = await assignRequestToPro(requestId, user.id)
      if (result.success) {
        await loadDashboardData()
      } else {
        setError(result.error || 'Erreur lors de l\'assignation')
      }
    } catch (err) {
      console.error('Erreur lors de l\'assignation:', err)
      setError('Erreur lors de l\'assignation')
    } finally {
      // Always remove from in-progress set
      setAssignRequestInProgress(prev => {
        const newSet = new Set(prev)
        newSet.delete(requestId)
        return newSet
      })
    }
  }

  const handleCreateQuote = async (requestId: string, templateId?: string) => {
    if (!user || createQuoteInProgress.has(requestId)) return

    // Add to in-progress set
    setCreateQuoteInProgress(prev => new Set(prev).add(requestId))

    try {
      const result = await createQuoteForRequest(requestId, user.id, templateId)
      if (result.success && result.data) {
        // Success - close dialog and navigate
        setShowCreateQuote(null)
        setSelectedTemplate("")
        router.push(`/pro/quote-editor/${result.data.id}`)
      } else {
        // Error - keep dialog open and show error
        setError(result.error || 'Erreur lors de la création du devis')
      }
    } catch (err) {
      console.error('Erreur lors de la création du devis:', err)
      setError('Erreur lors de la création du devis')
    } finally {
      // Always remove from in-progress set
      setCreateQuoteInProgress(prev => {
        const newSet = new Set(prev)
        newSet.delete(requestId)
        return newSet
      })
    }
  }

  const handleSendQuote = async (quoteId: string) => {
    if (sendQuoteInProgress.has(quoteId)) return

    // Add to in-progress set
    setSendQuoteInProgress(prev => new Set(prev).add(quoteId))

    try {
      const result = await sendQuoteToClientAction(quoteId)
      if (result.success) {
        await loadDashboardData()
      } else {
        setError(result.error || 'Erreur lors de l\'envoi')
      }
    } catch (err) {
      console.error('Erreur lors de l\'envoi:', err)
      setError('Erreur lors de l\'envoi')
    } finally {
      // Always remove from in-progress set
      setSendQuoteInProgress(prev => {
        const newSet = new Set(prev)
        newSet.delete(quoteId)
        return newSet
      })
    }
  }

  const getStatusColor = (status: string) => {
    const colors = {
      'en_attente': 'secondary',
      'en_analyse': 'outline',
      'en_cours': 'default',
      'validé': 'default',
      'envoyé': 'default',
      'accepté': 'default',
      'refusé': 'destructive'
    } as const

    return colors[status as keyof typeof colors] || 'secondary'
  }

  const getPriorityLabel = (priority: number) => {
    const labels = {
      1: { label: 'Haute', variant: 'destructive' as const },
      2: { label: 'Normale', variant: 'secondary' as const },
      3: { label: 'Basse', variant: 'outline' as const }
    }

    return labels[priority as keyof typeof labels] || labels[2]
  }

  const filteredRequests = useMemo(() => requests.filter(request => {
    const haystack =
      (request.client_name ?? '') + ' ' +
      (request.client_email ?? '') + ' ' +
      (request.ai_summary ?? '')
    const matchesSearch = !searchTerm ||
      haystack.toLowerCase().includes(searchTerm.toLowerCase())

    const matchesStatus = statusFilter === 'all' || request.status === statusFilter
    const matchesPriority = priorityFilter === 'all' || request.priority.toString() === priorityFilter

    return matchesSearch && matchesStatus && matchesPriority
  }), [requests, searchTerm, statusFilter, priorityFilter])

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Chargement du tableau de bord...</p>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <main className="max-w-6xl mx-auto p-6">
        <Alert>
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Erreur</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </main>
    )
  }

  return (
    <main className="min-h-screen bg-gray-50/30">
      <header className="bg-white border-b">
        <div className="max-w-7xl mx-auto px-4 py-6">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Tableau de bord</h1>
              <p className="text-sm text-gray-600">
                Bienvenue {user?.name}, gérez vos demandes et devis
              </p>
            </div>
            <Button onClick={() => router.push('/pro/templates')}>
              <Plus className="h-4 w-4 mr-1" />
              Nouveau template
            </Button>
          </div>
        </div>
      </header>

      <div className="max-w-7xl mx-auto p-6 space-y-6">
        {/* Stats Cards */}
        {stats && (
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Demandes en attente</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.pending_requests}</p>
                  </div>
                  <Clock className="h-8 w-8 text-orange-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Devis envoyés</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.sent_quotes}</p>
                  </div>
                  <Send className="h-8 w-8 text-blue-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">Devis acceptés</p>
                    <p className="text-3xl font-bold text-gray-900">{stats.accepted_quotes}</p>
                  </div>
                  <CheckCircle2 className="h-8 w-8 text-green-500" />
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">CA accepté</p>
                    <p className="text-3xl font-bold text-gray-900">
                      {stats.total_accepted_amount.toLocaleString('fr-FR')} €
                    </p>
                  </div>
                  <Euro className="h-8 w-8 text-emerald-500" />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        <Tabs defaultValue="requests" className="space-y-6">
          <TabsList>
            <TabsTrigger value="requests">Demandes</TabsTrigger>
            <TabsTrigger value="quotes">Devis</TabsTrigger>
            <TabsTrigger value="templates">Templates</TabsTrigger>
          </TabsList>

          <TabsContent value="requests" className="space-y-4">
            {/* Filters */}
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Demandes clients</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="flex flex-col sm:flex-row gap-4 mb-6">
                  <div className="flex-1">
                    <div className="relative">
                      <Search className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
                      <Input
                        placeholder="Rechercher par client ou description..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="pl-10"
                      />
                    </div>
                  </div>

                  <Select value={statusFilter} onValueChange={setStatusFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Statut" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Tous les statuts</SelectItem>
                      <SelectItem value="en_attente">En attente</SelectItem>
                      <SelectItem value="en_analyse">En analyse</SelectItem>
                      <SelectItem value="en_cours">En cours</SelectItem>
                      <SelectItem value="validé">Validé</SelectItem>
                      <SelectItem value="envoyé">Envoyé</SelectItem>
                      <SelectItem value="accepté">Accepté</SelectItem>
                      <SelectItem value="refusé">Refusé</SelectItem>
                    </SelectContent>
                  </Select>

                  <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                    <SelectTrigger className="w-48">
                      <SelectValue placeholder="Priorité" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="all">Toutes priorités</SelectItem>
                      <SelectItem value="1">Haute</SelectItem>
                      <SelectItem value="2">Normale</SelectItem>
                      <SelectItem value="3">Basse</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Requests Table */}
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Client</TableHead>
                        <TableHead>Description</TableHead>
                        <TableHead>Statut</TableHead>
                        <TableHead>Priorité</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredRequests.map((request) => (
                        <TableRow key={request.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium">{request.client_name || 'Anonyme'}</div>
                              <div className="text-sm text-gray-500">{request.client_email}</div>
                            </div>
                          </TableCell>
                          <TableCell className="max-w-xs">
                            <div className="text-sm">
                              <div className="font-medium mb-1">
                                {request.input?.typeRenovation || 'Non spécifié'}
                              </div>
                              {request.ai_summary && (
                                <div className="text-gray-600 line-clamp-2">
                                  {request.ai_summary.slice(0, 120)}...
                                </div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            <Badge variant={getStatusColor(request.status)}>
                              {request.status.replace('_', ' ')}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <Badge variant={getPriorityLabel(request.priority).variant}>
                              {getPriorityLabel(request.priority).label}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {new Date(request.created_at).toLocaleDateString('fr-FR')}
                            </div>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              {request.status === 'en_attente' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  disabled={assignRequestInProgress.has(request.id)}
                                  onClick={() => handleAssignRequest(request.id)}
                                >
                                  {assignRequestInProgress.has(request.id) ? (
                                    <>
                                      <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-1" />
                                      Assignation...
                                    </>
                                  ) : (
                                    <>
                                      <Users className="h-4 w-4 mr-1" />
                                      Prendre
                                    </>
                                  )}
                                </Button>
                              )}

                              <Dialog
                                open={showCreateQuote === request.id}
                                onOpenChange={(open) => setShowCreateQuote(open ? request.id : null)}
                              >
                                <DialogTrigger asChild>
                                  <Button size="sm">
                                    <Plus className="h-4 w-4 mr-1" />
                                    Devis
                                  </Button>
                                </DialogTrigger>
                                <DialogContent>
                                  <DialogHeader>
                                    <DialogTitle>Créer un devis</DialogTitle>
                                  </DialogHeader>
                                  <div className="space-y-4">
                                    <div>
                                      <Label>Template (optionnel)</Label>
                                      <Select value={selectedTemplate} onValueChange={setSelectedTemplate}>
                                        <SelectTrigger>
                                          <SelectValue placeholder="Choisir un template" />
                                        </SelectTrigger>
                                        <SelectContent>
                                          <SelectItem value="">Nouveau devis vierge</SelectItem>
                                          {templates.map((template) => (
                                            <SelectItem key={template.id} value={template.id}>
                                              {template.name} ({template.category})
                                            </SelectItem>
                                          ))}
                                        </SelectContent>
                                      </Select>
                                    </div>
                                    <div className="flex justify-end gap-2">
                                      <Button
                                        variant="outline"
                                        onClick={() => setShowCreateQuote(null)}
                                      >
                                        Annuler
                                      </Button>
                                      <Button
                                        disabled={createQuoteInProgress.has(request.id)}
                                        onClick={() => handleCreateQuote(request.id, selectedTemplate || undefined)}
                                      >
                                        {createQuoteInProgress.has(request.id) ? "Création..." : "Créer le devis"}
                                      </Button>
                                    </div>
                                  </div>
                                </DialogContent>
                              </Dialog>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="quotes" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="text-lg">Devis récents</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="rounded-md border">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>N° Devis</TableHead>
                        <TableHead>Client</TableHead>
                        <TableHead>Montant TTC</TableHead>
                        <TableHead>Statut</TableHead>
                        <TableHead>Version</TableHead>
                        <TableHead>Date</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {quotes.map((quote) => (
                        <TableRow key={quote.id}>
                          <TableCell className="font-mono text-sm">
                            #{quote.id.slice(-8).toUpperCase()}
                          </TableCell>
                          <TableCell>
                            <div>
                              <div className="font-medium">{quote.client_name || 'Anonyme'}</div>
                              <div className="text-sm text-gray-500">{quote.client_email}</div>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">
                            {quote.total_ttc.toLocaleString('fr-FR')} €
                          </TableCell>
                          <TableCell>
                            <Badge variant={getStatusColor(quote.status)}>
                              {quote.status}
                            </Badge>
                          </TableCell>
                          <TableCell>v{quote.version}</TableCell>
                          <TableCell>
                            {new Date(quote.created_at).toLocaleDateString('fr-FR')}
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex items-center justify-end gap-2">
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => router.push(`/pro/quote-editor/${quote.id}`)}
                              >
                                <Edit className="h-4 w-4 mr-1" />
                                Modifier
                              </Button>

                              {quote.status === 'validé' && (
                                <Button
                                  size="sm"
                                  disabled={sendQuoteInProgress.has(quote.id)}
                                  onClick={() => handleSendQuote(quote.id)}
                                >
                                  <Send className="h-4 w-4 mr-1" />
                                  {sendQuoteInProgress.has(quote.id) ? "Envoi..." : "Envoyer"}
                                </Button>
                              )}
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="templates" className="space-y-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">Templates de devis</CardTitle>
                <Button onClick={() => router.push('/pro/templates/new')}>
                  <Plus className="h-4 w-4 mr-1" />
                  Nouveau template
                </Button>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                  {templates.map((template) => (
                    <Card key={template.id} className="hover:shadow-md transition-shadow">
                      <CardHeader className="pb-3">
                        <div className="flex items-start justify-between">
                          <div>
                            <CardTitle className="text-base">{template.name}</CardTitle>
                            <p className="text-sm text-gray-600 mt-1">
                              {template.category}
                            </p>
                          </div>
                          <Badge variant="secondary">
                            {template.usage_count} utilisations
                          </Badge>
                        </div>
                      </CardHeader>
                      <CardContent>
                        {template.description && (
                          <p className="text-sm text-gray-600 mb-3 line-clamp-2">
                            {template.description}
                          </p>
                        )}

                        <div className="flex items-center justify-between">
                          <div className="text-sm text-gray-500">
                            {template.items?.length || 0} postes
                          </div>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => router.push(`/pro/templates/${template.id}`)}
                          >
                            <Eye className="h-4 w-4 mr-1" />
                            Voir
                          </Button>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </main>
  )
}
