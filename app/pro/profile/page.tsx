"use client"

import { useEffect, useState } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Button } from "@/components/ui/button"
import { Alert, AlertTitle, AlertDescription } from "@/components/ui/alert"
import { useProAuth, type User } from "@/hooks/use-auth"

export default function ProProfilePage() {
  const router = useRouter()
  const { user, company, loading, updateProfile } = useProAuth()

  const [companyName, setCompanyName] = useState("")
  const [address, setAddress] = useState("")
  const [postalCode, setPostalCode] = useState("")
  const [city, setCity] = useState("")
  const [siret, setSiret] = useState("")

  const [saving, setSaving] = useState(false)
  const [success, setSuccess] = useState<string | null>(null)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    if (company) {
      setCompanyName(company.company_name || "")
      setAddress(company.address || "")
      setPostalCode(company.postal_code || "")
      setCity(company.city || "")
      setSiret(company.siret || "")
    } else if (user) {
      // fallback to user.name
      setCompanyName(user.name || "")
    }
  }, [company, user])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!user) return router.replace('/auth/login')

    setSaving(true)
    setError(null)
    setSuccess(null)

    try {
      const updates: Partial<Pick<User, 'name' | 'company_name' | 'address' | 'postal_code' | 'city' | 'siret'>> = {
        company_name: companyName || null,
        address: address || null,
        postal_code: postalCode || null,
        city: city || null,
        siret: siret || null
      }

      const res = await updateProfile(updates)
      if (res?.success) {
        setSuccess('Profil professionnel mis à jour')
      } else {
        setError(res?.error || 'Erreur lors de la sauvegarde')
      }
    } catch (err: unknown) {
      setError((err as Error)?.message || 'Erreur lors de la sauvegarde')
    } finally {
      setSaving(false)
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">Chargement...</div>
      </div>
    )
  }

  return (
    <main className="max-w-3xl mx-auto p-6">
      <Card>
        <CardHeader>
          <CardTitle>Profil professionnel</CardTitle>
        </CardHeader>
        <CardContent>
          {error && (
            <Alert variant="destructive" className="mb-4">
              <AlertTitle>Erreur</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}

          {success && (
            <Alert className="mb-4">
              <AlertTitle>Succès</AlertTitle>
              <AlertDescription>{success}</AlertDescription>
            </Alert>
          )}

          <form onSubmit={handleSubmit} className="space-y-4">
            <div>
              <Label htmlFor="company_name">Nom de l&apos;entreprise</Label>
              <Input id="company_name" value={companyName} onChange={(e) => setCompanyName(e.target.value)} />
            </div>

            <div>
              <Label htmlFor="address">Adresse</Label>
              <Input id="address" value={address} onChange={(e) => setAddress(e.target.value)} />
            </div>

            <div className="grid grid-cols-3 gap-4">
              <div>
                <Label htmlFor="postal_code">Code postal</Label>
                <Input
                  id="postal_code"
                  value={postalCode}
                  onChange={(e) => {
                    const cleaned = e.target.value.replace(/\D/g, '').slice(0, 5)
                    setPostalCode(cleaned)
                  }}
                  inputMode="numeric"
                  pattern="\d*"
                  maxLength={5}
                />
              </div>
              <div className="col-span-2">
                <Label htmlFor="city">Ville</Label>
                <Input id="city" value={city} onChange={(e) => setCity(e.target.value)} />
              </div>
            </div>

            <div>
              <Label htmlFor="siret">SIRET</Label>
              <Input
                id="siret"
                value={siret}
                onChange={(e) => {
                  const cleaned = e.target.value.replace(/\D/g, '').slice(0, 14)
                  setSiret(cleaned)
                }}
                inputMode="numeric"
                pattern="\d*"
                maxLength={14}
              />
            </div>

            <div className="flex items-center gap-3">
              <Button type="submit" disabled={saving}>
                {saving ? 'Enregistrement...' : 'Enregistrer'}
              </Button>
              <Button
                type="button"
                variant="outline"
                onClick={() => router.back()}
              >
                Retour
              </Button>
            </div>
          </form>
        </CardContent>
      </Card>
    </main>
  )
}
