/**
 * Tests for QuoteEditorPage and QuotePreview
 * Framework/Libraries: @testing-library/react + user-event + jest/vitest mocks
 *
 * Notes:
 * - We mock next/navigation (useParams/useRouter), the auth hook useProAuth,
 *   debounce hook useDebounce, and all async server actions used by the page.
 * - Tests focus on behaviors visible in the provided diff.
 */

import React from 'react'
import { render, screen, waitFor, within, act } from '@testing-library/react'
import userEvent from '@testing-library/user-event'

// The SUT (page component). In Next.js (app router) pages often default export the component.
// Adjust import if the actual file path differs in the repository.
import QuoteEditorPage from './page'

// Shared push mock for consistent assertions
const pushMock = vi.fn()

// Mock next/navigation hooks
vi.mock('next/navigation', () => {
  return {
    useParams: () => ({ id: 'quote_1234567890' }),
    useRouter: () => ({ push: pushMock }),
  }
})

// Mock auth hook
vi.mock('@/hooks/use-auth', () => ({
  useProAuth: () => ({ user: { id: 'pro_1' }, loading: false })
}))
// Mock debounce to return value immediately (bypass timers)
vi.mock('@/hooks/use-debounce', () => ({
  useDebounce: (value: any) => value
}))

// Mock server actions used inside the page
vi.mock('@/app/actions/quote-pro', () => {
  return {
    getQuoteWithItemsAction: vi.fn(),
    getQuoteHistoryAction: vi.fn(),
    updateQuoteAction: vi.fn(),
    updateQuoteItemAction: vi.fn(),
    addQuoteItemAction: vi.fn(),
    deleteQuoteItemAction: vi.fn(),
    sendQuoteToClientAction: vi.fn(),
    convertQuoteToInvoiceAction: vi.fn(),
  }
})

// Extract mocked actions for convenience
import {
  getQuoteWithItemsAction,
  getQuoteHistoryAction,
  updateQuoteAction,
  updateQuoteItemAction,
  addQuoteItemAction,
  deleteQuoteItemAction,
  sendQuoteToClientAction,
  convertQuoteToInvoiceAction,
} from '@/app/actions/quote-pro'

// Shared fixtures
const baseQuote = {
  id: 'q_abcdef123456',
  version: 3,
  status: 'brouillon',
  total_ht: 1000,
  total_ttc: 1200,
  tva_rate: 20,
  margin_rate: 10,
  validity_days: 30,
  internal_comments: '',
  custom_conditions: '',
  ai_generated_data: {
    summary: 'Résumé IA',
    aiConfidence: 0.85,
  },
  items: [
    {
      id: 'item_1',
      description: 'Peinture des murs',
      category: 'peinture',
      quantity: 10,
      unit: 'm2',
      unit_price: 20,
      total_price: 200,
      ai_suggested: true,
    },
    {
      id: 'item_2',
      description: 'Pose de parquet',
      category: 'menuiserie',
      quantity: 5,
      unit: 'm2',
      unit_price: 80,
      total_price: 400,
      ai_suggested: false,
    },
  ],
}

const validValidation = {
  isValid: true,
  errors: [],
  warnings: ['Attention au délai de livraison'],
}

const invalidValidation = {
  isValid: false,
  errors: ['Quantité manquante sur un poste', 'TVA incohérente'],
  warnings: [],
}

// Helper to arrange initial successful load
const arrangeLoadSuccess = (quote = baseQuote, validation = validValidation, history = []) => {
  (getQuoteWithItemsAction as any).mockResolvedValueOnce({
    success: true,
    data: { quote, validation },
  })
  ;(getQuoteHistoryAction as any).mockResolvedValueOnce({
    success: true,
    data: history.length ? history : [
      { id: 'h1', action: 'créé', created_at: new Date('2024-01-01T10:00:00Z').toISOString() },
      { id: 'h2', action: 'modifié', created_at: new Date('2024-02-02T12:00:00Z').toISOString(), comment: 'MAJ TVA' },
    ],
  })
}

describe('QuoteEditorPage', () => {
  beforeEach(() => {
    vi.clearAllMocks()
  })

  test('shows loading state while fetching', async () => {
    // Delay the promise to keep loading state visible briefly
    (getQuoteWithItemsAction as any).mockImplementationOnce(() => new Promise(() => {}))
    render(<QuoteEditorPage />)
    expect(screen.getByText(/Chargement du devis/i)).toBeInTheDocument()
  })

  test('renders error alert when quote fails to load', async () => {
    ;(getQuoteWithItemsAction as any).mockResolvedValueOnce({
      success: false,
      error: 'Erreur lors du chargement du devis',
    })
    render(<QuoteEditorPage />)
    expect(await screen.findByText(/Erreur/)).toBeInTheDocument()
    expect(screen.getByText(/Erreur lors du chargement du devis/)).toBeInTheDocument()
  })

  test('renders header with status badge, version and short id; preview dialog opens', async () => {
    arrangeLoadSuccess()
    render(<QuoteEditorPage />)

    // Header content
    expect(await screen.findByText('Éditeur de devis')).toBeInTheDocument()
    expect(screen.getByText('brouillon')).toBeInTheDocument()
    expect(screen.getByText(/v3/)).toBeInTheDocument()
    expect(screen.getByText(new RegExp(`#${baseQuote.id.slice(-8)}`))).toBeInTheDocument()

    // Open preview dialog
    const previewBtn = screen.getByRole('button', { name: /Prévisualiser/i })
    await userEvent.click(previewBtn)
    expect(await screen.findByText(/Prévisualisation du devis/i)).toBeInTheDocument()
    // A piece of QuotePreview content
    expect(screen.getByText(/Détail des travaux/i)).toBeInTheDocument()
  })

  test('Envoyer button disabled when validation invalid; enabled when valid', async () => {
    arrangeLoadSuccess(baseQuote, invalidValidation)
    render(<QuoteEditorPage />)
    const sendBtn = await screen.findByRole('button', { name: /Envoyer/i })
    expect(sendBtn).toBeDisabled()

    // Rerender with valid validation
    vi.clearAllMocks()
    arrangeLoadSuccess(baseQuote, validValidation)
    render(<QuoteEditorPage />)
    const sendBtn2 = await screen.findByRole('button', { name: /Envoyer/i })
    expect(sendBtn2).toBeEnabled()
  })

  test('clicking Valider updates status to validé via updateQuoteAction', async () => {
    arrangeLoadSuccess()
    ;(updateQuoteAction as any).mockResolvedValueOnce({ success: true })
    render(<QuoteEditorPage />)

    const validateBtn = await screen.findByRole('button', { name: /Valider/i })
    await userEvent.click(validateBtn)

    await waitFor(() => {
      expect(updateQuoteAction).toHaveBeenCalledWith({
        quoteId: baseQuote.id,
        updates: { status: 'validé' },
      })
    })

    // Badge reflects new status
    expect(await screen.findByText('validé')).toBeInTheDocument()
  })

  test('editing item quantity calls updateQuoteItemAction with parsed float', async () => {
    arrangeLoadSuccess()
    ;(updateQuoteItemAction as any).mockResolvedValueOnce({ success: true })
    render(<QuoteEditorPage />)

    const row = await screen.findByRole('row', { name: /Peinture des murs/i })
    const qtyInput = within(row).getByRole('spinbutton')
    await userEvent.clear(qtyInput)
    await userEvent.type(qtyInput, '12.5')

    await waitFor(() => {
      expect(updateQuoteItemAction).toHaveBeenCalledWith({
        itemId: 'item_1',
        updates: { quantity: 12.5 },
      })
    })
  })

  test('adding a new item via dialog appends and resets form', async () => {
    arrangeLoadSuccess()
    ;(addQuoteItemAction as any).mockResolvedValueOnce({
      success: true,
      data: {
        id: 'item_3',
        description: 'Nettoyage',
        category: 'autre',
        quantity: 1,
        unit: 'u',
        unit_price: 50,
        total_price: 50,
        ai_suggested: false,
      },
    })
    render(<QuoteEditorPage />)

    await userEvent.click(await screen.findByRole('button', { name: /Ajouter un poste/i }))

    const desc = await screen.findByPlaceholderText(/Description du poste/i)
    await userEvent.type(desc, 'Nettoyage')

    // Quantity and PU
    const qty = screen.getByLabelText('Quantité')
    const pu = screen.getByLabelText('Prix unitaire')
    await userEvent.clear(qty); await userEvent.type(qty, '1')
    await userEvent.clear(pu); await userEvent.type(pu, '50')

    // Add
    await userEvent.click(screen.getByRole('button', { name: /^Ajouter$/ }))

    await waitFor(() => {
      expect(addQuoteItemAction).toHaveBeenCalledWith({
        quoteId: baseQuote.id,
        item: expect.objectContaining({ description: 'Nettoyage', order_index: baseQuote.items.length }),
      })
    })

    // New row visible
    expect(await screen.findByRole('row', { name: /Nettoyage/i })).toBeInTheDocument()
  })

  test('deleting an item removes it from the table', async () => {
    arrangeLoadSuccess()
    ;(deleteQuoteItemAction as any).mockResolvedValueOnce({ success: true })
    render(<QuoteEditorPage />)

    const row = await screen.findByRole('row', { name: /Pose de parquet/i })
    const delBtn = within(row).getByRole('button', { name: '' }) // icon button with no accessible name
    await userEvent.click(delBtn)

    await waitFor(() => {
      expect(deleteQuoteItemAction).toHaveBeenCalledWith('item_2', baseQuote.id)
    })

    // It should eventually disappear
    await waitFor(() => {
      expect(screen.queryByRole('row', { name: /Pose de parquet/i })).not.toBeInTheDocument()
    })
  })

  test('sending quote sets status to envoyé and navigates to dashboard', async () => {
    arrangeLoadSuccess()
    ;(sendQuoteToClientAction as any).mockResolvedValueOnce({ success: true })

    render(<QuoteEditorPage />)

    const sendBtn = await screen.findByRole('button', { name: /Envoyer/i })
    await userEvent.click(sendBtn)

    await waitFor(() => {
      expect(sendQuoteToClientAction).toHaveBeenCalledWith(baseQuote.id)
    })
    expect(await screen.findByText('envoyé')).toBeInTheDocument()
    expect(pushMock).toHaveBeenCalledWith('/pro/dashboard')
  })

  test('convert to invoice is visible when status=accepté and navigates on success', async () => {
    const accepted = { ...baseQuote, status: 'accepté' }
    arrangeLoadSuccess(accepted)
    ;(convertQuoteToInvoiceAction as any).mockResolvedValueOnce({ success: true })

    render(<QuoteEditorPage />)
    const convertBtn = await screen.findByRole('button', { name: /Convertir en facture/i })
    await userEvent.click(convertBtn)

    await waitFor(() => {
      expect(convertQuoteToInvoiceAction).toHaveBeenCalledWith(accepted.id)
      expect(pushMock).toHaveBeenCalledWith('/pro/dashboard')
    })
  })

  test('shows destructive alert when validation.isValid is false and lists errors', async () => {
    arrangeLoadSuccess(baseQuote, invalidValidation)
    render(<QuoteEditorPage />)

    expect(await screen.findByText(/Devis invalide/)).toBeInTheDocument()
    expect(screen.getByText(/Quantité manquante/)).toBeInTheDocument()
    expect(screen.getByText(/TVA incohérente/)).toBeInTheDocument()
  })

  test('shows warnings when present', async () => {
    arrangeLoadSuccess(baseQuote, validValidation)
    render(<QuoteEditorPage />)

    expect(await screen.findByText(/Avertissements/)).toBeInTheDocument()
    expect(screen.getByText(/Attention au délai/)).toBeInTheDocument()
  })

  test('settings: changing marge and TVA triggers updateQuoteAction via debounced values', async () => {
    arrangeLoadSuccess()
    ;(updateQuoteAction as any).mockResolvedValue({ success: true }) // resolve for both calls
    render(<QuoteEditorPage />)

    const margeInput = await screen.findByLabelText(/Marge/i)
    await userEvent.clear(margeInput)
    await userEvent.type(margeInput, '12.3')

    const tvaInput = screen.getByLabelText(/TVA/i)
    await userEvent.clear(tvaInput)
    await userEvent.type(tvaInput, '19.6')

    await waitFor(() => {
      expect(updateQuoteAction).toHaveBeenCalledWith({
        quoteId: baseQuote.id,
        updates: { margin_rate: 12.3 },
      })
    })
    await waitFor(() => {
      expect(updateQuoteAction).toHaveBeenCalledWith({
        quoteId: baseQuote.id,
        updates: { tva_rate: 19.6 },
      })
    })
  })

  test('history tab renders entries with actions and dates', async () => {
    arrangeLoadSuccess()
    render(<QuoteEditorPage />)

    // Switch to "Historique"
    const tabBtn = await screen.findByRole('tab', { name: /Historique/ })
    await userEvent.click(tabBtn)

    expect(await screen.findByText(/créé/)).toBeInTheDocument()
    expect(screen.getByText(/modifié/)).toBeInTheDocument()
  })

  test('reload button calls getQuoteWithItemsAction again', async () => {
    arrangeLoadSuccess()
    render(<QuoteEditorPage />)

    const reload = await screen.findByRole('button', { name: /Recharger/i })
    await userEvent.click(reload)

    await waitFor(() => {
      expect(getQuoteWithItemsAction).toHaveBeenCalledTimes(2)
    })
  })
})

describe('QuotePreview', () => {
  // Import the named component if it is exported; otherwise we open the preview and assert content there.
  // Here we validate key computed pieces visible in the preview dialog.
  test('renders totals and TVA details correctly', async () => {
    arrangeLoadSuccess()
    render(<QuoteEditorPage />)

    await userEvent.click(await screen.findByRole('button', { name: /Prévisualiser/i }))

    // Totals area
    expect(await screen.findByText(/Sous-total HT/i)).toBeInTheDocument()
    expect(screen.getAllByText(/€/).length).toBeGreaterThan(0)
    // TVA line shows rate
    expect(screen.getByText(/TVA \(20%\)/)).toBeInTheDocument()
    // Total TTC text
    expect(screen.getByText(/Total TTC/)).toBeInTheDocument()
  })
})
