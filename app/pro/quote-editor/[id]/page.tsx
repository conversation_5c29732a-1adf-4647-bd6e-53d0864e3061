"use client"

import { useEffect, useState } from "react"
import { use<PERSON><PERSON><PERSON>, useR<PERSON><PERSON> } from "next/navigation"
import { <PERSON><PERSON>, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON>alog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Separator } from "@/components/ui/separator"
import { Textarea } from "@/components/ui/textarea"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>rigger } from "@/components/ui/tabs"
import {
  CheckCircle2,
  Eye,
  Send,
  TriangleAlert,
  Plus,
  Trash2,
  History,
  Copy,
  FileText,
  Settings,
  RefreshCw
} from "lucide-react"
import {
  getQuoteWithItemsAction,
  updateQuoteAction,
  addQuoteItemAction,
  updateQuoteItemAction,
  deleteQuoteItemAction,
  sendQuoteToClientAction,
  convertQuoteToInvoiceAction,
  getQuoteHistoryAction
} from "@/app/actions/quote-pro"
import { useProAuth } from "@/hooks/use-auth"
import { useDebounce } from "@/hooks/use-debounce"
import type { UpdateQuotePayload, UpdateQuoteItemPayload } from "@/app/actions/quote-pro"
import type { QuoteWithItems, QuoteItemRecord, QuoteHistoryRecord, AiGeneratedData, Category, Unit } from "@/lib/types"

import { calculateQuoteTotals } from "@/lib/quote-utils"

// Helper function to compute item total price
const computeItemTotalPrice = (item: any) => {
  return Math.round((item.quantity * item.unit_price * item.complexity_factor) * 100) / 100
}

export default function QuoteEditorPage() {
  const params = useParams<{ id: string }>()
  const router = useRouter()
  const { user, company, loading: authLoading } = useProAuth()
  const [quote, setQuote] = useState<QuoteWithItems | null>(null)
  const [history, setHistory] = useState<QuoteHistoryRecord[]>([])
  const [loading, setLoading] = useState(true)
  const [saving, setSaving] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [validation, setValidation] = useState<any>(null)

  // Debounced state for settings
  const [marginRate, setMarginRate] = useState(0)
  const [tvaRate, setTvaRate] = useState(0)
  const debouncedMarginRate = useDebounce(marginRate, 500)
  const debouncedTvaRate = useDebounce(tvaRate, 500)

  // States for new item dialog
  const [showAddItem, setShowAddItem] = useState(false)
  const [newItem, setNewItem] = useState({
    category: "autre" as Category,
    description: "",
    quantity: 1,
    unit: "u" as Unit,
    unit_price: 0,
    material_cost: 0,
    labor_cost: 0,
    complexity_factor: 1,
    is_optional: false,
    supplier: "",
    notes: ""
  })

  useEffect(() => {
    if (params.id) {
      loadQuote(params.id)
      loadHistory(params.id)
    }
  }, [params.id])

  useEffect(() => {
    if (quote) {
      setMarginRate(quote.margin_rate)
      setTvaRate(quote.tva_rate)
    }
  }, [quote])

  useEffect(() => {
    if (debouncedMarginRate !== quote?.margin_rate) {
      handleUpdateQuote({ margin_rate: debouncedMarginRate })
    }
  }, [debouncedMarginRate])

  useEffect(() => {
    if (debouncedTvaRate !== quote?.tva_rate) {
      handleUpdateQuote({ tva_rate: debouncedTvaRate })
    }
  }, [debouncedTvaRate])


  const loadQuote = async (quoteId: string) => {
    setLoading(true)
    try {
      const result = await getQuoteWithItemsAction(quoteId)
      if (result.success && result.data) {
        setQuote(result.data.quote)
        setValidation(result.data.validation)
      } else {
        setError(result.error || "Erreur lors du chargement du devis")
      }
    } catch (err) {
      setError("Erreur lors du chargement du devis")
    } finally {
      setLoading(false)
    }
  }

  const loadHistory = async (quoteId: string) => {
    try {
      const result = await getQuoteHistoryAction(quoteId)
      if (result.success) {
        setHistory(result.data || [])
      }
    } catch (err) {
      console.error("Erreur lors du chargement de l'historique:", err)
    }
  }

  const handleUpdateQuote = async (updates: UpdateQuotePayload['updates']) => {
    if (!quote) return

    setSaving(true)
    try {
      const result = await updateQuoteAction({
        quoteId: quote.id,
        updates
      })

      if (result.success && result.data) {
        setQuote(prev => {
          if (!prev) return null
          const next = { ...prev, ...result.data, ...updates }
          const totals = calculateQuoteTotals(next.items || [], next.margin_rate, next.tva_rate)
          return { ...next, total_ht: totals.total_ht, total_ttc: totals.total_ttc }
        })
      } else {
        setError(result.error || "Erreur lors de la mise à jour")
      }
    } catch (err) {
      setError("Erreur lors de la mise à jour")
    } finally {
      setSaving(false)
    }
  }

  const handleUpdateItem = async (itemId: string, updates: UpdateQuoteItemPayload['updates']) => {
    try {
      const result = await updateQuoteItemAction({ itemId, updates })
      if (result.success && result.data) {
        setQuote(prev => {
          if (!prev) return null
          const updatedItems = (prev.items || []).map(item =>
            item.id === itemId ? { ...item, ...updates, total_price: computeItemTotalPrice({ ...item, ...updates }) } : item
          )

          const newTotals = calculateQuoteTotals(updatedItems, prev.margin_rate, prev.tva_rate)

          return {
            ...prev,
            items: updatedItems,
            total_ht: newTotals.total_ht,
            total_ttc: newTotals.total_ttc,
          }
        })
      }
    } catch (err) {
      setError("Erreur lors de la mise à jour de l'item")
    }
  }

  const handleAddItem = async () => {
    if (!quote) return

    try {
      const result = await addQuoteItemAction({
        quoteId: quote.id,
        item: {
          ...newItem,
          order_index: (quote.items?.length || 0)
        }
      })

      if (result.success) {
        setQuote(prev => {
          if (!prev) return null
          const newItemWithTotal = { ...result.data, total_price: computeItemTotalPrice(result.data) } as QuoteItemRecord
          const items = [...(prev.items || []), newItemWithTotal]
          const totals = calculateQuoteTotals(items, prev.margin_rate, prev.tva_rate)
          return { ...prev, items, total_ht: totals.total_ht, total_ttc: totals.total_ttc }
        })
        setShowAddItem(false)
        setNewItem({
          category: "autre",
          description: "",
          quantity: 1,
          unit: "u",
          unit_price: 0,
          material_cost: 0,
          labor_cost: 0,
          complexity_factor: 1,
          is_optional: false,
          supplier: "",
          notes: ""
        })
      }
    } catch (err) {
      setError("Erreur lors de l'ajout de l'item")
    }
  }

  const handleDeleteItem = async (itemId: string) => {
    if (!quote) return

    try {
      const result = await deleteQuoteItemAction(itemId, quote.id)
      if (result.success) {
         setQuote(prev => {
           if (!prev) return null
           const items = (prev.items || []).filter(item => item.id !== itemId)
           const totals = calculateQuoteTotals(items, prev.margin_rate, prev.tva_rate)
           return { ...prev, items, total_ht: totals.total_ht, total_ttc: totals.total_ttc }
         })
      }
    } catch (err) {
      setError("Erreur lors de la suppression de l'item")
    }
  }

  const handleSendQuote = async () => {
    if (!quote) return

    try {
      const result = await sendQuoteToClientAction(quote.id)
      if (result.success) {
        setQuote(prev => prev ? { ...prev, status: 'envoyé' } : null)
        router.push("/pro/dashboard")
      } else {
        setError(result.error || "Erreur lors de l'envoi")
      }
    } catch (err) {
      setError("Erreur lors de l'envoi")
    }
  }

  const handleConvertToInvoice = async () => {
    if (!quote) return

    try {
      const result = await convertQuoteToInvoiceAction(quote.id)
      if (result.success) {
        router.push("/pro/dashboard")
      } else {
        setError(result.error || "Erreur lors de la conversion")
      }
    } catch (err) {
      setError("Erreur lors de la conversion")
    }
  }

  if (authLoading || loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto mb-4"></div>
          <p>Chargement du devis...</p>
        </div>
      </div>
    )
  }

  if (error || !quote) {
    return (
      <main className="max-w-4xl mx-auto p-6">
        <Alert>
          <TriangleAlert className="h-4 w-4" />
          <AlertTitle>Erreur</AlertTitle>
          <AlertDescription>{error || "Devis introuvable"}</AlertDescription>
        </Alert>
      </main>
    )
  }

  const statusColor = {
    'brouillon': 'secondary',
    'en_revision': 'outline',
    'validé': 'default',
    'envoyé': 'default',
    'accepté': 'default',
    'refusé': 'destructive'
  } as const

  return (
    <main className="min-h-screen">
      <header className="border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="max-w-7xl mx-auto px-4 py-4 flex items-center justify-between">
          <div className="flex items-center gap-3">
            <h1 className="text-xl font-semibold">Éditeur de devis</h1>
            <Badge variant={statusColor[quote.status as keyof typeof statusColor]}>
              {quote.status}
            </Badge>
            <span className="text-sm text-muted-foreground">
              v{quote.version} • #{quote.id.slice(-8)}
            </span>
          </div>

          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => handleUpdateQuote({ status: 'validé' })}
              disabled={saving}
            >
              <CheckCircle2 className="h-4 w-4 mr-1" />
              Valider
            </Button>

            <Dialog>
              <DialogTrigger asChild>
                <Button variant="outline" size="sm">
                  <Eye className="h-4 w-4 mr-1" />
                  Prévisualiser
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-4xl max-h-[80vh] overflow-y-auto">
                <DialogHeader>
                  <DialogTitle>Prévisualisation du devis</DialogTitle>
                </DialogHeader>
                  {/* Build a company object from the logged-in pro profile (useProAuth())
                      The `profiles` table may or may not contain company fields; we
                      provide sensible fallbacks so the preview never crashes. */}
                  <QuotePreview
                    quote={quote}
                    company={company || {
                      company_name: user?.name || 'Entreprise BTP',
                      address: '',
                      postal_code: '',
                      city: '',
                      siret: ''
                    }}
                  />
              </DialogContent>
            </Dialog>

            <Button
              onClick={handleSendQuote}
              disabled={saving || !validation?.isValid}
              size="sm"
            >
              <Send className="h-4 w-4 mr-1" />
              Envoyer
            </Button>
            <Button variant="outline" size="sm" onClick={() => router.push('/pro/profile')}>
              <Settings className="h-4 w-4 mr-1" />
              Profil
            </Button>
          </div>
        </div>
      </header>

      {error && (
        <Alert className="mx-4 mt-4">
          <TriangleAlert className="h-4 w-4" />
          <AlertTitle>Erreur</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      {validation && !validation.isValid && (
        <Alert className="mx-4 mt-4" variant="destructive">
          <TriangleAlert className="h-4 w-4" />
          <AlertTitle>Devis invalide</AlertTitle>
          <AlertDescription>
            <ul className="list-disc list-inside">
              {validation.errors.map((error: string, index: number) => (
                <li key={index}>{error}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      {validation?.warnings && validation.warnings.length > 0 && (
        <Alert className="mx-4 mt-4">
          <TriangleAlert className="h-4 w-4" />
          <AlertTitle>Avertissements</AlertTitle>
          <AlertDescription>
            <ul className="list-disc list-inside">
              {validation.warnings.map((warning: string, index: number) => (
                <li key={index}>{warning}</li>
              ))}
            </ul>
          </AlertDescription>
        </Alert>
      )}

      <div className="max-w-7xl mx-auto p-4">
        <Tabs defaultValue="editor" className="space-y-6">
          <TabsList>
            <TabsTrigger value="editor">Éditeur</TabsTrigger>
            <TabsTrigger value="settings">Paramètres</TabsTrigger>
            <TabsTrigger value="history">Historique</TabsTrigger>
          </TabsList>

          <TabsContent value="editor" className="space-y-6">
            <div className="grid lg:grid-cols-3 gap-6">
              {/* Informations de base */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Informations</CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {quote.ai_generated_data && (
                    <div className="text-sm text-muted-foreground bg-muted/30 p-3 rounded">
                      <strong>Analyse IA:</strong>
                      <p className="mt-2">{quote.ai_generated_data.summary}</p>
                      <div className="mt-2 text-xs">
                        Confiance: {Math.round(((quote.ai_generated_data.aiConfidence ?? (quote.ai_generated_data as any).ai_confidence ?? 0.7) as number) * 100)}%
                      </div>
                    </div>
                  )}

                  <div className="space-y-2">
                    <Label htmlFor="internal_comments">Commentaires internes</Label>
                    <Textarea
                      id="internal_comments"
                      defaultValue={quote.internal_comments || ""}
                      onBlur={(e) => handleUpdateQuote({ internal_comments: e.target.value })}
                      rows={4}
                      placeholder="Notes internes pour ce devis..."
                    />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="custom_conditions">Conditions particulières</Label>
                    <Textarea
                      id="custom_conditions"
                      defaultValue={quote.custom_conditions || ""}
                      onBlur={(e) => handleUpdateQuote({ custom_conditions: e.target.value })}
                      rows={3}
                      placeholder="Conditions spécifiques à ce devis..."
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Totaux */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Totaux</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-sm">Sous-total HT</span>
                      <span className="font-medium">{quote.total_ht.toFixed(2)} €</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-sm">TVA ({quote.tva_rate}%)</span>
                      <span className="font-medium">
                        {(quote.total_ttc - quote.total_ht).toFixed(2)} €
                      </span>
                    </div>
                    <Separator />
                    <div className="flex justify-between text-lg font-semibold">
                      <span>Total TTC</span>
                      <span>{quote.total_ttc.toFixed(2)} €</span>
                    </div>

                    {quote.status === 'accepté' && (
                      <Button
                        onClick={handleConvertToInvoice}
                        className="w-full mt-4"
                        size="sm"
                      >
                        <FileText className="h-4 w-4 mr-1" />
                        Convertir en facture
                      </Button>
                    )}
                  </div>
                </CardContent>
              </Card>

              {/* Actions rapides */}
              <Card>
                <CardHeader>
                  <CardTitle className="text-lg">Actions</CardTitle>
                </CardHeader>
                <CardContent className="space-y-2">
                  <Button variant="outline" className="w-full" size="sm">
                    <Copy className="h-4 w-4 mr-1" />
                    Dupliquer
                  </Button>
                  <Button variant="outline" className="w-full" size="sm">
                    <FileText className="h-4 w-4 mr-1" />
                    Créer template
                  </Button>
                  <Button
                    variant="outline"
                    className="w-full"
                    size="sm"
                    disabled={saving}
                    onClick={() => loadQuote(quote.id)}
                  >
                    <RefreshCw className="h-4 w-4 mr-1" />
                    Recharger
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* Table des items */}
            <Card>
              <CardHeader className="flex flex-row items-center justify-between">
                <CardTitle className="text-lg">Postes du devis</CardTitle>
                <Dialog open={showAddItem} onOpenChange={setShowAddItem}>
                  <DialogTrigger asChild>
                    <Button size="sm">
                      <Plus className="h-4 w-4 mr-1" />
                      Ajouter un poste
                    </Button>
                  </DialogTrigger>
                  <DialogContent>
                    <DialogHeader>
                      <DialogTitle>Ajouter un poste</DialogTitle>
                    </DialogHeader>
                    <div className="space-y-4">
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="category">Catégorie</Label>
                          <Select
                            value={newItem.category}
                            onValueChange={(value) => setNewItem({...newItem, category: value as Category})}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="électricité">Électricité</SelectItem>
                              <SelectItem value="plomberie">Plomberie</SelectItem>
                              <SelectItem value="maçonnerie">Maçonnerie</SelectItem>
                              <SelectItem value="peinture">Peinture</SelectItem>
                              <SelectItem value="menuiserie">Menuiserie</SelectItem>
                              <SelectItem value="isolation">Isolation</SelectItem>
                              <SelectItem value="démolition">Démolition</SelectItem>
                              <SelectItem value="autre">Autre</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="unit">Unité</Label>
                          <Select
                            value={newItem.unit}
                            onValueChange={(value) => setNewItem({...newItem, unit: value as Unit})}
                          >
                            <SelectTrigger>
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="u">Unité</SelectItem>
                              <SelectItem value="m2">m²</SelectItem>
                              <SelectItem value="ml">ml</SelectItem>
                              <SelectItem value="h">Heures</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>

                      <div>
                        <Label htmlFor="description">Description</Label>
                        <Input
                          id="description"
                          value={newItem.description}
                          onChange={(e) => setNewItem({...newItem, description: e.target.value})}
                          placeholder="Description du poste"
                        />
                      </div>

                      <div className="grid grid-cols-3 gap-4">
                        <div>
                          <Label htmlFor="quantity">Quantité</Label>
                          <Input
                            id="quantity"
                            type="number"
                            step="0.01"
                            value={newItem.quantity}
                            onChange={(e) => setNewItem({...newItem, quantity: parseFloat(e.target.value) || 0})}
                          />
                        </div>
                        <div>
                          <Label htmlFor="unit_price">Prix unitaire</Label>
                          <Input
                            id="unit_price"
                            type="number"
                            step="0.01"
                            value={newItem.unit_price}
                            onChange={(e) => setNewItem({...newItem, unit_price: parseFloat(e.target.value) || 0})}
                          />
                        </div>
                        <div>
                          <Label>Total</Label>
                          <div className="h-10 px-3 py-2 border rounded-md bg-muted text-sm">
                            {(newItem.quantity * newItem.unit_price).toFixed(2)} €
                          </div>
                        </div>
                      </div>

                      <div className="flex justify-end gap-2">
                        <Button variant="outline" onClick={() => setShowAddItem(false)}>
                          Annuler
                        </Button>
                        <Button onClick={handleAddItem} disabled={!newItem.description}>
                          Ajouter
                        </Button>
                      </div>
                    </div>
                  </DialogContent>
                </Dialog>
              </CardHeader>
              <CardContent>
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>Description</TableHead>
                      <TableHead className="w-20">Qté</TableHead>
                      <TableHead className="w-20">Unité</TableHead>
                      <TableHead className="w-24">PU HT</TableHead>
                      <TableHead className="w-24">Total HT</TableHead>
                      <TableHead className="w-16"></TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {quote.items?.map((item) => (
                      <TableRow key={item.id}>
                        <TableCell>
                          <div>
                            <div className="font-medium">{item.description}</div>
                            <div className="text-xs text-muted-foreground">
                              {item.category}
                              {item.ai_suggested && (
                                <Badge variant="secondary" className="ml-2 text-xs">IA</Badge>
                              )}
                            </div>
                          </div>
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            value={item.quantity}
                            onChange={(e) => handleUpdateItem(item.id, {
                              quantity: parseFloat(e.target.value) || 0
                            })}
                            className="w-16 h-8"
                          />
                        </TableCell>
                        <TableCell>
                          <Select
                            value={item.unit}
                            onValueChange={(value) => handleUpdateItem(item.id, { unit: value })}
                          >
                            <SelectTrigger className="w-20 h-8">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="u">Unité</SelectItem>
                              <SelectItem value="m2">m²</SelectItem>
                              <SelectItem value="ml">ml</SelectItem>
                              <SelectItem value="h">Heures</SelectItem>
                            </SelectContent>
                          </Select>
                        </TableCell>
                        <TableCell>
                          <Input
                            type="number"
                            step="0.01"
                            min="0"
                            value={item.unit_price}
                            onChange={(e) => handleUpdateItem(item.id, {
                              unit_price: parseFloat(e.target.value) || 0
                            })}
                            className="w-20 h-8"
                          />
                        </TableCell>
                        <TableCell className="font-medium">
                          {item.total_price.toFixed(2)} €
                        </TableCell>
                        <TableCell>
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteItem(item.id)}
                            className="h-8 w-8 p-0 text-destructive hover:text-destructive"
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="settings" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Paramètres du devis</CardTitle>
              </CardHeader>
              <CardContent className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="margin_rate">Marge (%)</Label>
                    <Input
                      id="margin_rate"
                      type="number"
                      step="0.1"
                      value={marginRate}
                      onChange={(e) => setMarginRate(parseFloat(e.target.value) || 0)}
                    />
                  </div>
                  <div>
                    <Label htmlFor="tva_rate">TVA (%)</Label>
                    <Input
                      id="tva_rate"
                      type="number"
                      step="0.1"
                      value={tvaRate}
                      onChange={(e) => setTvaRate(parseFloat(e.target.value) || 0)}
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="validity_days">Validité (jours)</Label>
                  <Input
                    id="validity_days"
                    type="number"
                    value={quote.validity_days}
                    onChange={(e) => handleUpdateQuote({
                      validity_days: parseInt(e.target.value) || 30
                    })}
                  />
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="history" className="space-y-4">
            {history.map((entry) => (
              <Card key={entry.id}>
                <CardContent className="pt-4">
                  <div className="flex items-start justify-between">
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{entry.action}</Badge>
                        <span className="text-sm text-muted-foreground">
                          {new Date(entry.created_at).toLocaleString('fr-FR')}
                        </span>
                      </div>
                      {entry.comment && (
                        <p className="text-sm">{entry.comment}</p>
                      )}
                    </div>
                    <History className="h-4 w-4 text-muted-foreground" />
                  </div>
                </CardContent>
              </Card>
            ))}
          </TabsContent>
        </Tabs>
      </div>
    </main>
  )
}

function QuotePreview({ quote, company }: { quote: QuoteWithItems; company?: {
  company_name?: string
  address?: string
  postal_code?: string
  city?: string
  siret?: string
} }) {
  const companyName = company?.company_name || 'Entreprise BTP'
  const addressLine = company?.address || '123 Rue de la Rénovation'
  const postalCity = (company?.postal_code || '75000') + (company?.city ? (' ' + company.city) : ' Paris')
  const siret = company?.siret || '*********** 00012'

  return (
    <div className="space-y-6 bg-white text-black p-8">
      <div className="flex justify-between items-start">
        <div>
          <h1 className="text-2xl font-bold">DEVIS</h1>
          <p className="text-sm text-gray-600">N° {quote.id.slice(-8).toUpperCase()}</p>
        </div>
        <div className="text-right">
          <div className="font-semibold">{companyName}</div>
          <div className="text-sm text-gray-600">
            {addressLine}<br/>
            {postalCity}<br/>
            {siret && <span>SIRET: {siret}</span>}
          </div>
        </div>
      </div>

      <div className="border-t border-b py-4">
        <p className="text-sm text-gray-600">
          Date: {new Date().toLocaleDateString('fr-FR')}
        </p>
        <p className="text-sm text-gray-600">
          Validité: {quote.validity_days} jours
        </p>
      </div>

      <div>
        <h2 className="text-lg font-semibold mb-4">Détail des travaux</h2>
        <table className="w-full text-sm">
          <thead>
            <tr className="border-b">
              <th className="text-left py-2">Désignation</th>
              <th className="text-right py-2 w-16">Qté</th>
              <th className="text-right py-2 w-16">Unité</th>
              <th className="text-right py-2 w-20">PU HT</th>
              <th className="text-right py-2 w-20">Total HT</th>
            </tr>
          </thead>
          <tbody>
            {quote.items?.map((item) => (
              <tr key={item.id} className="border-b">
                <td className="py-2">
                  <div className="font-medium">{item.description}</div>
                  <div className="text-xs text-gray-500">{item.category}</div>
                </td>
                <td className="text-right py-2">{item.quantity}</td>
                <td className="text-right py-2">{item.unit}</td>
                <td className="text-right py-2">{item.unit_price.toFixed(2)} €</td>
                <td className="text-right py-2 font-medium">{item.total_price.toFixed(2)} €</td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      <div className="flex justify-end">
        <div className="w-64 space-y-2">
          <div className="flex justify-between">
            <span>Sous-total HT</span>
            <span>{quote.total_ht.toFixed(2)} €</span>
          </div>
          <div className="flex justify-between">
            <span>TVA ({quote.tva_rate}%)</span>
            <span>{(quote.total_ttc - quote.total_ht).toFixed(2)} €</span>
          </div>
          <div className="flex justify-between border-t pt-2 text-lg font-semibold">
            <span>Total TTC</span>
            <span>{quote.total_ttc.toFixed(2)} €</span>
          </div>
        </div>
      </div>

      {quote.custom_conditions && (
        <div>
          <h3 className="font-semibold mb-2">Conditions particulières</h3>
          <p className="text-sm whitespace-pre-wrap">{quote.custom_conditions}</p>
        </div>
      )}

      <div className="text-xs text-gray-500 border-t pt-4">
        <p>Devis gratuit - Valable {quote.validity_days} jours</p>
        <p>Travaux soumis à TVA au taux de {quote.tva_rate}%</p>
      </div>
    </div>
  )
}
