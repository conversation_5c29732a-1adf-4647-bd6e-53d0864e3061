"use client"

import React from "react"

import { use<PERSON>allback, useMemo, useRef, useState } from "react"
import { UploadCloud, FileText, ImageIcon, X } from "lucide-react"
import { cn } from "@/lib/utils"

interface FileDropzoneProps {
  accept?: string[]
  maxFiles?: number
  onFilesChange?: (files: File[]) => void
  className?: string
  defaultText?: string
}

export default function FileDropzone({
  accept = ["image/*", "application/pdf"],
  maxFiles = 10,
  onFilesChange,
  className,
  defaultText = "Glissez-déposez vos fichiers ici (photos, PDF) ou cliquez pour parcourir",
}: FileDropzoneProps) {
  const inputRef = useRef<HTMLInputElement>(null)
  const [files, setFiles] = useState<File[]>([])
  const [isOver, setIsOver] = useState(false)

  const onSelect = useCallback(
    (newFiles: FileList | null) => {
      if (!newFiles) return
      const arr = Array.from(newFiles)
      const filtered = arr.filter((f) => accept.some((a) => matchAccept(f, a))).slice(0, maxFiles)
      const merged = [...files, ...filtered].slice(0, maxFiles)
      setFiles(merged)
      onFilesChange?.(merged)
    },
    [files, accept, maxFiles, onFilesChange],
  )

  const onDrop = useCallback(
    (e: React.DragEvent) => {
      e.preventDefault()
      setIsOver(false)
      onSelect(e.dataTransfer.files)
    },
    [onSelect],
  )

  const remove = useCallback(
    (name: string) => {
      const next = files.filter((f) => f.name !== name)
      setFiles(next)
      onFilesChange?.(next)
    },
    [files, onFilesChange],
  )

  const previews = useMemo(() => {
    const promises = files.map(async (f) => {
      const isImg = f.type.startsWith("image/")
      let url = ""

      if (isImg) {
        try {
          url = await new Promise<string>((resolve, reject) => {
            const reader = new FileReader()
            reader.onload = () => resolve(reader.result as string)
            reader.onerror = () => reject(new Error(`Failed to read file: ${f.name}`))
            reader.readAsDataURL(f)
          })
        } catch (error) {
          console.error(`Error creating preview for ${f.name}:`, error)
          url = "/placeholder.svg"
        }
      }

      return { name: f.name, type: f.type, url, isImg }
    })

    return Promise.all(promises)
  }, [files])

  // State to hold resolved previews
  const [resolvedPreviews, setResolvedPreviews] = useState<{ name: string; type: string; url: string; isImg: boolean }[]>([])
  const previewToken = useRef(0)

  // Resolve previews when they change
  React.useEffect(() => {
    const token = ++previewToken.current
    previews
      .then((result) => {
        if (previewToken.current === token) {
          setResolvedPreviews(result)
        }
      })
      .catch((err) => console.error("Preview generation failed:", err))
  }, [previews])

  // Cleanup any remaining object URLs on unmount (safety cleanup)
  React.useEffect(() => {
    return () => {
      // No object URLs to clean up since we're using data URLs
    }
  }, [])

  return (
    <div className={cn("space-y-3", className)}>
      <div
        role="button"
        tabIndex={0}
        onClick={() => inputRef.current?.click()}
        onKeyDown={(e) => e.key === "Enter" && inputRef.current?.click()}
        onDragOver={(e) => {
          e.preventDefault()
          setIsOver(true)
        }}
        onDragLeave={() => setIsOver(false)}
        onDrop={onDrop}
        className={cn(
          "rounded-lg border border-dashed p-6 text-center cursor-pointer transition-colors",
          isOver ? "bg-muted" : "bg-background",
        )}
        aria-label="Zone de dépôt de fichiers"
      >
        <UploadCloud className="mx-auto h-6 w-6 mb-2" />
        <p className="text-sm text-muted-foreground">{defaultText}</p>
        <input
          ref={inputRef}
          type="file"
          multiple
          className="hidden"
          accept={accept.join(",")}
          onChange={(e) => onSelect(e.target.files)}
        />
      </div>

      {resolvedPreviews.length > 0 && (
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {resolvedPreviews.map((p) => (
            <div key={p.name} className="relative rounded border overflow-hidden">
              <button
                className="absolute right-1 top-1 z-10 bg-background/80 rounded-full p-1"
                onClick={(e) => {
                  e.stopPropagation()
                  remove(p.name)
                }}
                aria-label={`Supprimer ${p.name}`}
              >
                <X className="h-4 w-4" />
              </button>
              {p.isImg ? (
                // eslint-disable-next-line @next/next/no-img-element
                <img
                  src={p.url || "/placeholder.svg"}
                  alt={`Aperçu ${p.name}`}
                  className="h-28 w-full object-cover"
                />
              ) : (
                <div className="h-28 w-full flex items-center justify-center bg-muted">
                  <FileText className="h-6 w-6 mr-2" />
                  <span className="text-xs break-all px-2">{p.name}</span>
                </div>
              )}
              <div className="px-2 py-1 text-xs truncate" title={p.name}>
                {p.isImg ? (
                  <span className="inline-flex items-center gap-1">
                    <ImageIcon className="h-3 w-3" /> {p.name}
                  </span>
                ) : (
                  p.name
                )}
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  )
}

function matchAccept(file: File, pattern: string) {
  if (pattern === "*/*") return true
  if (pattern.endsWith("/*")) {
    const type = pattern.split("/")[0]
    return file.type.startsWith(type + "/")
  }
  return file.type === pattern
}
