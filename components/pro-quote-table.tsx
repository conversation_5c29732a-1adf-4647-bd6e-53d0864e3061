"use client"

import { useMemo } from "react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Plus, Trash2 } from "lucide-react"
import type { LineItem, Quote, QuoteTotals } from "@/lib/types"
import { recalcTotals } from "@/lib/quote"

interface Props {
  quote: Quote
  onChange: (q: Quote) => void
}

export default function ProQuoteTable({ quote, onChange }: Props) {
  const totals = useMemo(() => quote.totals, [quote])
  const updateTotals = (items: LineItem[], t: Partial<QuoteTotals> = {}) => {
    const { subtotal, vatAmount, total } = recalcTotals(
      items,
      t.vatRate ?? totals.vatRate,
      t.margin ?? totals.margin,
      t.discount ?? totals.discount,
    )
    onChange({
      ...quote,
      items,
      totals: {
        subtotal,
        vatAmount,
        total,
        margin: t.margin ?? totals.margin,
        vatRate: t.vatRate ?? totals.vatRate,
        discount: t.discount ?? totals.discount,
      },
    })
  }

  const updateItem = (id: string, patch: Partial<LineItem>) => {
    const items = quote.items.map((it) =>
      it.id === id
        ? {
            ...it,
            ...patch,
            total: Math.round((patch.quantity ?? it.quantity) * (patch.unitPrice ?? it.unitPrice) * 100) / 100,
          }
        : it,
    )
    updateTotals(items)
  }

  const removeItem = (id: string) => {
    const items = quote.items.filter((i) => i.id !== id)
    updateTotals(items)
  }

  const addItem = () => {
    const newItem: LineItem = {
      id: `add_${Math.random().toString(36).slice(2, 8)}`,
      label: "Nouveau poste",
      category: "autre",
      quantity: 1,
      unit: "u",
      unitPrice: 0,
      total: 0,
      editable: true,
    }
    const items = [newItem, ...quote.items]
    updateTotals(items)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="text-sm text-muted-foreground">
          Ecart vs estimation IA:{" "}
          <strong>
            {(100 * ((quote.totals.total - quote.aiEstimateTotal) / quote.aiEstimateTotal || 0)).toFixed(0)}%
          </strong>{" "}
          (cible ±20%)
        </div>
        <Button variant="outline" size="sm" onClick={addItem}>
          <Plus className="h-4 w-4 mr-1" /> Ajouter un poste
        </Button>
      </div>
      <div className="rounded-md border overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Description</TableHead>
              <TableHead>Catégorie</TableHead>
              <TableHead className="w-[100px]">Qté</TableHead>
              <TableHead>Unité</TableHead>
              <TableHead>PU (HT)</TableHead>
              <TableHead>Total (HT)</TableHead>
              <TableHead></TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {quote.items.map((it) => (
              <TableRow key={it.id}>
                <TableCell>
                  <Input
                    aria-label="Description"
                    value={it.label}
                    onChange={(e) => updateItem(it.id, { label: e.target.value })}
                  />
                </TableCell>
                <TableCell>
                  <Input
                    aria-label="Catégorie"
                    value={it.category}
                    onChange={(e) => updateItem(it.id, { category: e.target.value as any })}
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    min={0}
                    step="0.1"
                    aria-label="Quantité"
                    value={it.quantity}
                    onChange={(e) => updateItem(it.id, { quantity: Number(e.target.value) || 0 })}
                  />
                </TableCell>
                <TableCell>
                  <Input
                    aria-label="Unité"
                    value={it.unit}
                    onChange={(e) => updateItem(it.id, { unit: e.target.value as any })}
                  />
                </TableCell>
                <TableCell>
                  <Input
                    type="number"
                    min={0}
                    step="0.1"
                    aria-label="Prix unitaire"
                    value={it.unitPrice}
                    onChange={(e) => updateItem(it.id, { unitPrice: Number(e.target.value) || 0 })}
                  />
                </TableCell>
                <TableCell className="font-medium">{it.total.toFixed(2)} €</TableCell>
                <TableCell className="text-right">
                  <Button variant="ghost" size="icon" onClick={() => removeItem(it.id)} aria-label="Supprimer">
                    <Trash2 className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {quote.items.length === 0 && (
              <TableRow>
                <TableCell colSpan={7} className="text-center text-sm text-muted-foreground">
                  Aucun poste. Ajoutez-en un.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="grid sm:grid-cols-2 gap-3">
        <div className="space-y-2">
          <label className="text-sm">Marge (HT)</label>
          <Input
            type="number"
            min={0}
            step="1"
            value={quote.totals.margin}
            onChange={(e) =>
              updateTotals(quote.items, { margin: Math.max(0, Math.round(Number(e.target.value) || 0)) })
            }
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm">Remise (HT)</label>
          <Input
            type="number"
            min={0}
            step="1"
            value={quote.totals.discount}
            onChange={(e) =>
              updateTotals(quote.items, { discount: Math.max(0, Math.round(Number(e.target.value) || 0)) })
            }
          />
        </div>
        <div className="space-y-2">
          <label className="text-sm">TVA</label>
          <Input
            type="number"
            min={0}
            max={1}
            step="0.01"
            value={quote.totals.vatRate}
            onChange={(e) =>
              updateTotals(quote.items, { vatRate: Math.min(1, Math.max(0, Number(e.target.value) || 0)) })
            }
          />
        </div>
      </div>

      <div className="ml-auto w-full sm:max-w-xs space-y-1">
        <div className="flex justify-between text-sm">
          <span>Sous-total</span>
          <span>{quote.totals.subtotal.toFixed(2)} €</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Marge</span>
          <span>{quote.totals.margin.toFixed(2)} €</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>Remise</span>
          <span>-{quote.totals.discount.toFixed(2)} €</span>
        </div>
        <div className="flex justify-between text-sm">
          <span>TVA ({Math.round(quote.totals.vatRate * 100)}%)</span>
          <span>{quote.totals.vatAmount.toFixed(2)} €</span>
        </div>
        <div className="flex justify-between font-semibold text-base">
          <span>Total TTC</span>
          <span>{quote.totals.total.toFixed(2)} €</span>
        </div>
      </div>
    </div>
  )
}
