"use client"

import Link from "next/link"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ArrowRight, Clock } from "lucide-react"
import type { RequestRecord } from "@/lib/types"
import { cn } from "@/lib/utils"

export default function RequestCard({ req }: { req: RequestRecord }) {
  const statusLabel: Record<RequestRecord["status"], string> = {
    en_attente: "En attente",
    en_analyse: "En analyse",
    en_cours: "En cours",
    validé: "Validé",
    envoyé: "Envoyé",
    accepté: "Accepté",
    refusé: "Refusé",
  }

  return (
    <Card className="h-full">
      <CardHeader className="space-y-2">
        <div className="flex items-center justify-between">
          <CardTitle className="text-base">Demande #{req.id.slice(-5).toUpperCase()}</CardTitle>
          <Badge
            className={cn(
              req.status === "en_attente" && "bg-amber-100 text-amber-900",
              req.status === "en_cours" && "bg-purple-100 text-purple-900",
              req.status === "validé" && "bg-emerald-100 text-emerald-900",
              req.status === "envoyé" && "bg-slate-200 text-slate-900",
              req.status === "accepté" && "bg-green-200 text-green-900",
              req.status === "refusé" && "bg-red-100 text-red-900",
            )}
          >
            {statusLabel[req.status]}
          </Badge>
        </div>
        <div className="text-xs text-muted-foreground line-clamp-2">{req.input.description}</div>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex gap-2">
          {req.previews.images.slice(0, 3).map((img) => (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              key={img.url}
              src={img.url || "/placeholder.svg?height=64&width=64&query=renovation%20photo%20preview"}
              alt="Aperçu"
              className="h-12 w-12 rounded object-cover border"
            />
          ))}
          {req.previews.images.length === 0 && (
            // eslint-disable-next-line @next/next/no-img-element
            <img
              src={"/placeholder.svg?height=64&width=64&query=renovation%20placeholder"}
              alt="Aperçu"
              className="h-12 w-12 rounded object-cover border"
            />
          )}
        </div>
        <div className="text-xs text-muted-foreground flex items-center gap-1">
          <Clock className="h-3.5 w-3.5" />
          Reçu le {new Date(req.createdAt).toLocaleString("fr-FR")}
        </div>
        <div className="flex justify-end">
          <Link href={`/pro/quote-editor/${req.id}`} className="inline-flex items-center text-sm hover:underline">
            Ouvrir <ArrowRight className="h-4 w-4 ml-1" />
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
