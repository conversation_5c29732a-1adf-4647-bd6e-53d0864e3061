import { createClient } from '@supabase/supabase-js'
import { config } from 'dotenv'

// Load environment variables
config()

// Use environment variables instead of hardcoded credentials
const supabaseUrl = process.env.VITE_SUPABASE_URL || process.env.SUPABASE_URL
const supabaseAnonKey = process.env.VITE_SUPABASE_ANON_KEY || process.env.SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Missing Supabase credentials. Please set VITE_SUPABASE_URL and VITE_SUPABASE_ANON_KEY environment variables.')
  console.error('💡 Create a .env file with your Supabase credentials or set them as environment variables.')
  process.exit(1)
}

const supabase = createClient(supabaseUrl, supabaseAnonKey)

async function testAuth() {
  console.log('Starting authentication test...')

  try {
    // Test login
    console.log('1. Attempting to sign <NAME_EMAIL>...')
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123',
    })

    if (authError) {
      console.error('❌ Auth error:', authError)
      return
    }

    console.log('✅ Authentication successful')
    console.log('User ID:', authData.user.id)
    console.log('User email:', authData.user.email)
    console.log('Session exists:', !!authData.session)

    // Test profile fetch
    console.log('\n2. Attempting to fetch profile...')
    const { data: profile, error: profileError } = await supabase
      .from('profiles')
      .select('*')
      .eq('id', authData.user.id)
      .maybeSingle()

    if (profileError) {
      console.error('❌ Profile fetch error:', profileError)
      console.error('Error details:', JSON.stringify(profileError, null, 2))
    } else if (!profile) {
      console.log('⚠️  No profile found for user')
    } else {
      console.log('✅ Profile fetched successfully')
      console.log('Profile:', JSON.stringify(profile, null, 2))
    }

    // Test session info
    console.log('\n3. Getting current session...')
    const { data: { session }, error: sessionError } = await supabase.auth.getSession()

    if (sessionError) {
      console.error('❌ Session error:', sessionError)
    } else if (session) {
      console.log('✅ Session is valid')
      console.log('Session user ID:', session.user.id)
      console.log('Access token exists:', !!session.access_token)
    } else {
      console.log('⚠️  No active session')
    }

    // Test with explicit auth context
    console.log('\n4. Testing profile fetch with explicit session...')
    if (session) {
      const { data: profile2, error: profileError2 } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', session.user.id)
        .maybeSingle()

      if (profileError2) {
        console.error('❌ Second profile fetch error:', profileError2)
      } else {
        console.log('✅ Second profile fetch successful:', !!profile2)
      }
    }

  } catch (error) {
    console.error('💥 Unexpected error:', error)
  }
}

testAuth().then(() => {
  console.log('\nTest completed.')
  process.exit(0)
}).catch(console.error)
