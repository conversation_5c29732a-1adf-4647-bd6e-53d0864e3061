"use client"

import { useEffect, useState } from "react"
import { useRouter } from "next/navigation"
import { supabase } from "@/lib/supabase-browser"
import type { AuthChangeEvent, Session } from "@supabase/supabase-js"

export interface User {
  id: string
  email?: string
  name?: string
  role: 'client' | 'pro' | 'admin'
  company_name?: string
  address?: string
  postal_code?: string
  city?: string
  siret?: string
  created_at: string
}

export function useAuth() {
  const [user, setUser] = useState<User | null>(null)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    // Get initial session
    getUser()

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event: AuthChangeEvent, session: Session | null) => {
        if (event === 'SIGNED_IN' && session) {
          await getUser()
        } else if (event === 'SIGNED_OUT') {
          setUser(null)
          setLoading(false)
        }
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  const getUser = async () => {
    try {
      setLoading(true)

      const { data: { session } } = await supabase.auth.getSession()

      if (session?.user) {
        // Get user profile from profiles table
        const { data: profile, error } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', session.user.id)
          .maybeSingle()

        if (error) {
          console.error('Error fetching profile:', error.message)
          setUser(null)
          return
        } else if (!profile) {
          // If no profile exists, create one
          const { data: newProfile } = await supabase
            .from('profiles')
            .insert([{
              id: session.user.id,
              email: session.user.email,
              name: session.user.user_metadata?.name || session.user.email?.split('@')[0],
              role: 'client' // Default role
            }])
            .select()
            .single()

          if (newProfile) {
            setUser(newProfile)
          } else {
            console.error('Failed to create profile for user:', session.user.id)
            setUser(null)
          }
        } else {
          setUser(profile)
        }
      } else {
        setUser(null)
      }
    } catch (error) {
      console.error('Error in getUser:', error instanceof Error ? error.message : String(error))
      setUser(null)
    } finally {
      setLoading(false)
    }
  }

  const signIn = async (email: string, password: string) => {
    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      })

      if (error) {
        throw error
      }

      return { success: true, data }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  const signUp = async (email: string, password: string, name?: string) => {
    try {
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            name: name || email.split('@')[0],
          }
        }
      })

      if (error) {
        throw error
      }

      return { success: true, data }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  const signOut = async () => {
    try {
      const { error } = await supabase.auth.signOut()

      if (error) {
        throw error
      }

      setUser(null)
      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  const updateProfile = async (updates: Partial<Pick<User, 'name' | 'company_name' | 'address' | 'postal_code' | 'city' | 'siret'>>) => {
    if (!user) return { success: false, error: 'No user logged in' }

    try {
      // Defensive filter
      const payload = Object.fromEntries(
        Object.entries(updates).filter(([, v]) => v !== undefined)
      )
      const { data, error } = await supabase
        .from('profiles')
        .update(payload)
        .eq('id', user.id)
        .select()
        .single()

      if (error) {
        throw error
      }

      setUser({ ...user, ...data })
      return { success: true, data }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email, {
        redirectTo: `${window.location.origin}/auth/reset-password`
      })

      if (error) {
        throw error
      }

      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  const updatePassword = async (newPassword: string) => {
    try {
      const { error } = await supabase.auth.updateUser({
        password: newPassword
      })

      if (error) {
        throw error
      }

      return { success: true }
    } catch (error: any) {
      return { success: false, error: error.message }
    }
  }

  return {
    user,
    // Convenience shaped company object for UI
    company: user ? {
      company_name: user.company_name || undefined,
      address: user.address || undefined,
      postal_code: user.postal_code || undefined,
      city: user.city || undefined,
      siret: user.siret || undefined
    } : null,
    loading,
    signIn,
    signUp,
    signOut,
    updateProfile,
    resetPassword,
    updatePassword,
    getUser,
    // Helper functions
    isAuthenticated: !!user,
    isPro: user?.role === 'pro' || user?.role === 'admin',
    isAdmin: user?.role === 'admin',
    isClient: user?.role === 'client'
  }
}

// Hook pour s'assurer qu'un utilisateur est connecté et a le bon rôle
export function useRequireAuth(requiredRole?: 'client' | 'pro' | 'admin') {
  const auth = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!auth.loading) {
      if (!auth.user) {
        // Redirect to login
        router.replace('/auth/login')
        return
      }

      if (requiredRole && auth.user.role !== requiredRole && auth.user.role !== 'admin') {
        // Unauthorized
        router.replace('/unauthorized')
        return
      }
    }
  }, [auth.loading, auth.user, requiredRole, router])

  return auth
}

// Hook spécifique pour les professionnels
export function useProAuth() {
  return useRequireAuth('pro')
}
