/* Test stack: Vitest + React Testing Library (hook utilities from @testing-library/react).
   These tests validate the useDebounce hook's timing behavior, cleanup, and support for various value types.
*/
import React from 'react';
import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { renderHook, act } from '@testing-library/react';
import * as ReactModule from 'react';

// Import the hook under test
import { useDebounce } from './use-debounce';

describe('useDebounce', () => {
  beforeEach(() => {
    vi.useFakeTimers()
  });
  afterEach(() => {
    vi.useRealTimers()
    vi.restoreAllMocks()
  });

  // …tests…
  it('should return the initial value immediately on mount', () => {
    const { result } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'hello', delay: 100 }
    });
    expect(result.current).toBe('hello');
  });

  it('should not update before delay elapses (basic debounce)', () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'start', delay: 100 }
    });

    // change value but do not advance timers enough
    rerender({ value: 'changed', delay: 100 });
    expect(result.current).toBe('start'); // still old value
    vi.advanceTimersByTime(50)
    expect(result.current).toBe('start'); // still not updated

    // after full delay, it should update
    vi.advanceTimersByTime(50)
    expect(result.current).toBe('changed');
  });

  it('should only apply the latest value after rapid successive changes', () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 0, delay: 100 }
    });

    rerender({ value: 1, delay: 100 });
    rerender({ value: 2, delay: 100 });
    rerender({ value: 3, delay: 100 });

    // Before delay elapses, still initial
    expect(result.current).toBe(0);

    // Run all pending timers – should settle on the last value (3)
    vi.runAllTimers()
    expect(result.current).toBe(3);
  });

  it('should reset the timer when delay prop changes', () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'A', delay: 100 }
    });

    // update value and shorten delay; timer should reset to new delay
    rerender({ value: 'B', delay: 20 });

    // Advance less than old delay but more than new delay
    vi.advanceTimersByTime(50)
    expect(result.current).toBe('B');
  });

  it('should handle 0 delay as immediate microtask scheduling', () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'x', delay: 0 }
    });

    rerender({ value: 'y', delay: 0 });

    // With fake timers, advancing by 0 flushes 0ms timeouts
    vi.advanceTimersByTime(100)
    expect(result.current).toBe('y');
  });

  it('should accept and return complex values (objects/arrays) after delay', () => {
    const initial = { a: 1 };
    const next = { a: 2, nested: { b: 3 } };

    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: initial, delay: 80 }
    });

    rerender({ value: next, delay: 80 });

    // Not yet updated
    expect(result.current).toBe(initial);

    vi.runAllTimers()
    expect(result.current).toBe(next);
  });

  it('cleans up timeout on unmount to avoid stray updates', () => {
    const clearSpy = vi.spyOn(global, 'clearTimeout');
    const { unmount, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'one', delay: 100 }
    });

    rerender({ value: 'two', delay: 100 });
    unmount();

    expect(clearSpy).toHaveBeenCalled();
    clearSpy.mockRestore();
  });

  it('does not update after unmount even if timer would have fired', () => {
    const { unmount, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'keep', delay: 100 }
    });

    rerender({ value: 'drop', delay: 100 });
    unmount();

    // Advance timers; no errors should be thrown and no state updates attempted
    // (React would warn on state update after unmount; absence of thrown error is our proxy)
    expect(() => { vi.runAllTimers() }).not.toThrow();
  });

  it('re-runs effect when both value and delay change together', () => {
    const { result, rerender } = renderHook(({ value, delay }) => useDebounce(value, delay), {
      initialProps: { value: 'init', delay: 200 }
    });

    rerender({ value: 'next', delay: 10 });
    expect(result.current).toBe('init');
    vi.advanceTimersByTime(100)
    expect(result.current).toBe('next');
  });
});
