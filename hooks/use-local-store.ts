"use client"

import { useCallback, useEffect, useMemo, useState } from "react"
import type { RequestRecord, Status } from "@/lib/types"

const KEY = "renov:requests"
const INC_KEY = "renov:invoice_inc"

function safeParse<T>(raw: string | null, fallback: T): T {
  if (!raw) return fallback
  try {
    return JSON.parse(raw) as T
  } catch {
    return fallback
  }
}

export function useRequests() {
  const [requests, setRequests] = useState<RequestRecord[]>([])

  useEffect(() => {
    const data = safeParse<RequestRecord[]>(localStorage.getItem(KEY), [])
    setRequests(data)
  }, [])

  useEffect(() => {
    localStorage.setItem(KEY, JSON.stringify(requests))
  }, [requests])

  const addRequest = useCallback((r: RequestRecord) => {
    setRequests((prev) => [r, ...prev])
  }, [])

  const updateRequest = useCallback((id: string, patch: Partial<RequestRecord>) => {
    setRequests((prev) => prev.map((r) => (r.id === id ? { ...r, ...patch, updatedAt: new Date().toISOString() } : r)))
  }, [])

  const getById = useCallback(
    (id: string) => {
      return requests.find((r) => r.id === id)
    },
    [requests],
  )

  const byStatus = useMemo(() => {
    const map: Record<Status, RequestRecord[]> = {
      en_attente: [],
      en_analyse: [],
      en_cours: [],
      validé: [],
      envoyé: [],
      accepté: [],
      refusé: [],
    }
    for (const r of requests) {
      map[r.status]?.push(r)
    }
    return map
  }, [requests])

  const nextInvoiceNo = useCallback(() => {
    const inc = Number(localStorage.getItem(INC_KEY) || "0") + 1
    localStorage.setItem(INC_KEY, String(inc))
    const now = new Date()
    return `F-${now.getFullYear()}-${inc.toString().padStart(4, "0")}`
  }, [])

  return { requests, setRequests, addRequest, updateRequest, getById, byStatus, nextInvoiceNo }
}
