/**
 * Test framework: Vitest
 * These tests validate:
 * - generateTextWithTimeout: resolves on time, times out on slow/never-resolving calls, propagates rejection, forwards options
 * - generateObjectWithTimeout: same behaviors as above
 */

import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { z } from 'zod';

// Mock external dependency: 'ai' BEFORE importing the module under test
vi.mock('ai', () => {
  return {
    generateText: vi.fn(),
    generateObject: vi.fn(),
  };
});

// Import mocked functions from 'ai'
import { generateText, generateObject } from 'ai';

// Module under test - import AFTER mock is set up
import { generateTextWithTimeout, generateObjectWithTimeout } from './ai-utils';

describe('ai-utils timeout wrappers', () => {
  beforeEach(() => {
    vi.useFakeTimers();
    vi.spyOn(global, 'setTimeout'); // Optional: verify timers are set
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.useRealTimers();
    vi.restoreAllMocks();
  });

  describe('generateTextWithTimeout', () => {
    it('resolves when generateText resolves before the timeout (happy path)', async () => {
      const mockResult = { text: 'ok' } as any;
      (generateText as unknown as ReturnType<typeof vi.fn>).mockImplementation(async () => {
        // Simulate small async delay
        await Promise.resolve();
        return mockResult;
      });

      const options = { model: 'dummy-model', prompt: 'Hello' } as any;
      const p = generateTextWithTimeout(options);

      // No need to advance timers; promise resolves naturally
      await expect(p).resolves.toBe(mockResult);
      expect(generateText).toHaveBeenCalledTimes(1);
      expect(generateText).toHaveBeenCalledWith(options);
    });

    it('rejects with timeout error when generateText never resolves', async () => {
      (generateText as unknown as ReturnType<typeof vi.fn>).mockImplementation(
        () => new Promise(() => { /* never resolves */ })
      );

      const options = { model: 'dummy-model', prompt: 'slow' } as any;
      const p = generateTextWithTimeout(options);

      // Advance virtual time to trigger timeout (30,000 ms)
      vi.advanceTimersByTime(30000);

      await expect(p).rejects.toThrowError('Operation timed out after 30000ms');
      expect(generateText).toHaveBeenCalledTimes(1);
    });

    it('propagates original rejection if generateText rejects before timeout', async () => {
      const err = new Error('upstream failure');
      (generateText as unknown as ReturnType<typeof vi.fn>).mockRejectedValue(err);

      const p = generateTextWithTimeout({} as any);
      await expect(p).rejects.toBe(err);
    });

    it('returns the first settled result when both race: resolves beats timeout', async () => {
      // Resolve immediately; timeout will be scheduled but lose the race.
      (generateText as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ text: 'fast' } as any);

      const p = generateTextWithTimeout({} as any);
      // Even if we advance timers a bit, the promise should have already resolved.
      await expect(p).resolves.toEqual({ text: 'fast' });
      // Optional: confirm a timeout was scheduled
      expect(setTimeout).toHaveBeenCalled();
    });
  });

  describe('generateObjectWithTimeout', () => {
    it('resolves with object result when generateObject resolves before timeout', async () => {
      const UserSchema = z.object({ id: z.number(), name: z.string() });
      const mockResult = { object: { id: 1, name: 'Ada' } } as any;

      (generateObject as unknown as ReturnType<typeof vi.fn>).mockResolvedValue(mockResult);

      const options = { model: 'dummy-model', schema: UserSchema, prompt: 'user' } as any;
      const p = generateObjectWithTimeout<typeof UserSchema>(options);

      await expect(p).resolves.toBe(mockResult);
      expect(generateObject).toHaveBeenCalledTimes(1);
      expect(generateObject).toHaveBeenCalledWith(options);
    });

    it('times out when generateObject is too slow', async () => {
      (generateObject as unknown as ReturnType<typeof vi.fn>).mockImplementation(
        () => new Promise(() => { /* never resolves */ })
      );

      const p = generateObjectWithTimeout<any>({} as any);

      vi.advanceTimersByTime(30000);
      await expect(p).rejects.toThrowError('Operation timed out after 30000ms');
    });

    it('propagates rejection from generateObject when it rejects quickly', async () => {
      const err = new Error('schema mismatch');
      (generateObject as unknown as ReturnType<typeof vi.fn>).mockRejectedValue(err);

      await expect(generateObjectWithTimeout<any>({} as any)).rejects.toBe(err);
    });

    it('resolves wins against timeout when fast', async () => {
      (generateObject as unknown as ReturnType<typeof vi.fn>).mockResolvedValue({ object: { ok: true } } as any);

      const p = generateObjectWithTimeout<any>({} as any);
      await expect(p).resolves.toEqual({ object: { ok: true } });
      expect(setTimeout).toHaveBeenCalled();
    });
  });
});
