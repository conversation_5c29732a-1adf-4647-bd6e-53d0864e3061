import { generateObject, generateText, type GenerateObjectResult, type GenerateTextResult } from 'ai';
import { z } from 'zod';

const TIMEOUT_MS = 30000; // 30 seconds

class TimeoutError extends Error {
  name = 'TimeoutError'
  constructor(ms: number) {
    super(`Operation timed out after ${ms}ms`)
  }
}

async function withTimeout<T>(
  promise: Promise<T>,
  timeoutMs: number = TIMEOUT_MS
): Promise<T> {
  return new Promise<T>((resolve, reject) => {
    const id = setTimeout(() => reject(new TimeoutError(timeoutMs)), timeoutMs)
    promise.then(
      (v) => { clearTimeout(id); resolve(v) },
      (e) => { clearTimeout(id); reject(e) },
    )
  })
}

export async function generateTextWithTimeout(
  options: Parameters<typeof generateText>[0]
): Promise<GenerateTextResult<any>> {
  return withTimeout(generateText(options));
}

type ZSchema = z.ZodTypeAny

type GenerateObjectOptions<T extends ZSchema> =
  Omit<Parameters<typeof generateObject>[0], 'schema'> & { schema: T }

export async function generateObjectWithTimeout<T extends ZSchema>(
  options: GenerateObjectOptions<T>,
  timeoutMs?: number
): Promise<GenerateObjectResult<T>> {
  return withTimeout(generateObject<T>(options), timeoutMs ?? TIMEOUT_MS)
}

// Export internals for white-box testing if desired in the future.
// export { withTimeout, TIMEOUT_MS };
