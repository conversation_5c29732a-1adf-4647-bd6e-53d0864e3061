import type { Category } from "./types"

export const DEFAULT_VAT = 0.2

export const laborRates: Record<Category, number> = {
  électricité: 65,
  plomberie: 68,
  maçonnerie: 55,
  peinture: 45,
  menuiserie: 60,
  isolation: 50,
  démolition: 48,
  autre: 50,
}

export const productCatalog: Record<
  Category,
  { sku: string; label: string; unit: "m2" | "ml" | "u"; unitPrice: number }[]
> = {
  électricité: [
    { sku: "ELEC-CABLE", label: "Câbles et gaines", unit: "ml", unitPrice: 3.5 },
    { sku: "ELEC-PRISE", label: "Prise standard", unit: "u", unitPrice: 9.9 },
    { sku: "ELEC-DISJ", label: "Disjoncteur modulaire", unit: "u", unitPrice: 18.5 },
  ],
  plomberie: [
    { sku: "PLOMB-PVC", label: "Tuyauterie PVC", unit: "ml", unitPrice: 6.5 },
    { sku: "PLOMB-CU", label: "Tuyauterie cuivre", unit: "ml", unitPrice: 12.0 },
    { sku: "PLOMB-ROBINET", label: "Robinetterie standard", unit: "u", unitPrice: 49.0 },
  ],
  maçonnerie: [
    { sku: "MACO-CIMENT", label: "Mortier/ciment", unit: "u", unitPrice: 7.2 },
    { sku: "MACO-PARPAING", label: "Parpaing", unit: "u", unitPrice: 2.1 },
  ],
  peinture: [
    { sku: "PEINT-GLY", label: "Peinture intérieur (L)", unit: "u", unitPrice: 16.0 },
    { sku: "PEINT-SOUS", label: "Sous-couche (L)", unit: "u", unitPrice: 12.0 },
  ],
  menuiserie: [{ sku: "MENU-PLAQ", label: "Plaque bois/OSB", unit: "m2", unitPrice: 14.0 }],
  isolation: [{ sku: "ISO-LDVR", label: "Laine de verre", unit: "m2", unitPrice: 8.5 }],
  démolition: [{ sku: "DEMO-SAC", label: "Évacuation gravats (sac)", unit: "u", unitPrice: 6.0 }],
  autre: [{ sku: "AUTRE-DIV", label: "Fournitures diverses", unit: "u", unitPrice: 25.0 }],
}

export function complexityFactor(urgence: "basse" | "normale" | "haute", descriptionLength: number, fileCount: number) {
  let factor = 1
  if (urgence === "haute") factor += 0.2
  if (urgence === "basse") factor -= 0.05
  if (descriptionLength > 600) factor += 0.05
  if (fileCount >= 5) factor += 0.05
  return Number(factor.toFixed(2))
}

export const categoryKeywords: Record<Category, string[]> = {
  électricité: ["électri", "prise", "disjonct", "tableau", "câble", "gaine", "lumière", "spot"],
  plomberie: ["plomb", "fuite", "robinet", "évac", "siphon", "canalis", "chaudière"],
  maçonnerie: ["mur", "cloison", "dalle", "béton", "parpaing", "chape"],
  peinture: ["peintu", "peindre", "enduit", "ponçage", "ravalement"],
  menuiserie: ["porte", "fenêtre", "bois", "menuiser", "placard"],
  isolation: ["isolation", "isolant", "laine", "pont thermique"],
  démolition: ["démoli", "dépose", "casser", "abattre"],
  autre: [],
}
