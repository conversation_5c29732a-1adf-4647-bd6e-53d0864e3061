/**
 * Unit tests for lib/database.ts
 * Framework: Vitest.
 * These tests mock @supabase/supabase-js createClient and validate:
 *  - happy paths
 *  - error propagation with informative messages
 *  - defaulting logic and timestamp handling
 *  - query composition (from/table names, filters, order, payloads)
 */
import { vi, test, expect, describe, beforeEach, afterEach } from "vitest";
import type { QuoteRecord, QuoteItemRecord, TemplateRecord, ProductCatalogRecord, QuoteHistoryRecord, NotificationRecord, InvoiceRecord, QuoteWithItems, DashboardStats, QuoteItemTemplate } from "./types";

// Set env before importing the module to ensure createClient initializes with defined values
process.env.NEXT_PUBLIC_SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || "http://example.local";
process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || "anon_key";

// A generic result type used by our mock builders
type SupabaseResult<T = any> = Promise<{ data: T; error: any }>;

type ResultShape<T=any> = { data: T; error: any };

type Builder = {
  _result: ResultShape<any>;
  _calls: Array<{ method: string; args: any[] }>;
  insert: ReturnType<typeof vi.fn>;
  upsert: ReturnType<typeof vi.fn>;
  update: ReturnType<typeof vi.fn>;
  delete: ReturnType<typeof vi.fn>;
  select: ReturnType<typeof vi.fn>;
  eq: ReturnType<typeof vi.fn>;
  order: ReturnType<typeof vi.fn>;
  limit: ReturnType<typeof vi.fn>;
  textSearch: ReturnType<typeof vi.fn>;
  or: ReturnType<typeof vi.fn>;
  single: ReturnType<typeof vi.fn>;
};

class MockBuilder extends Promise<ResultShape<any>> implements Builder {
  _result: ResultShape<any>;
  _calls: Array<{ method: string; args: any[] }>;
  insert: ReturnType<typeof vi.fn>;
  upsert: ReturnType<typeof vi.fn>;
  update: ReturnType<typeof vi.fn>;
  delete: ReturnType<typeof vi.fn>;
  select: ReturnType<typeof vi.fn>;
  eq: ReturnType<typeof vi.fn>;
  order: ReturnType<typeof vi.fn>;
  limit: ReturnType<typeof vi.fn>;
  textSearch: ReturnType<typeof vi.fn>;
  or: ReturnType<typeof vi.fn>;
  single: ReturnType<typeof vi.fn>;

  constructor(result: ResultShape<any>) {
    super((resolve) => resolve(result));
    this._result = result;
    this._calls = [];

    const chain = (name: string) => vi.fn((...args: any[]) => {
      this._calls.push({ method: name, args });
      return this;
    });

    this.insert = chain("insert");
    this.upsert = chain("upsert");
    this.update = chain("update");
    this.delete = chain("delete");
    this.select = chain("select");
    this.eq = chain("eq");
    this.order = chain("order");
    this.limit = chain("limit");
    this.textSearch = chain("textSearch");
    this.or = chain("or");
    this.single = vi.fn(async () => result);
  }
}

class SupabaseMock {
  fromResults: Record<string, ResultShape<any>> = {};
  rpcResults: Record<string, ResultShape<any>> = {};
  builders: Record<string, Builder[]> = {};
  from = vi.fn((table: string): Builder => {
    const result = this.fromResults.hasOwnProperty(table)
      ? this.fromResults[table]
      : { data: null, error: null };

    const builder = new MockBuilder(result);

    if (!this.builders[table]) this.builders[table] = [];
    this.builders[table].push(builder);
    return builder;
  });
  rpc = vi.fn(async (fn: string, _params?: any) => {
    const res = this.rpcResults.hasOwnProperty(fn) ? this.rpcResults[fn] : { data: null, error: null };
    return res;
  });
  setFromResult<T=any>(table: string, res: ResultShape<T>) { this.fromResults[table] = res as any; }
  setRpcResult<T=any>(fn: string, res: ResultShape<T>) { this.rpcResults[fn] = res as any; }
  getLastBuilder(table: string): Builder | undefined {
    const arr = this.builders[table] || [];
    return arr[arr.length - 1];
  }
  reset() {
    this.fromResults = {};
    this.rpcResults = {};
    this.builders = {};
    this.from.mockClear();
    this.rpc.mockClear();
  }
}

const mockClient = new SupabaseMock();

// Mock @supabase/supabase-js so that createClient returns our mockClient
vi.mock("@supabase/supabase-js", () => {
  return {
    createClient: vi.fn(() => mockClient)
  };
});

// Mock @supabase/ssr so that createServerClient returns our mockClient
vi.mock("@supabase/ssr", () => {
  return {
    createServerClient: vi.fn(() => mockClient)
  };
});

// Import module under test after mocking
import * as db from "./database";

beforeEach(() => {
  vi.useFakeTimers();
  vi.setSystemTime(new Date("2025-01-02T03:04:05.000Z"));
  mockClient.reset();
});

afterEach(() => {
  vi.useRealTimers();
});

describe("QUOTES", () => {
  test("createQuote inserts with defaults and returns created record", async () => {
    const input = { request_id: "req1", created_by: "user1", ai_generated_data: { a: 1 }, template_id: "tpl1" };
    const fakeQuote: any = { id: "q1", request_id: "req1", created_by: "user1", version: 1, status: "brouillon" };
    mockClient.setFromResult("quotes", { data: fakeQuote, error: null });

    const result = await db.createQuote(input);

    expect(result).toBe(fakeQuote);
    expect(mockClient.from).toHaveBeenCalledWith("quotes");
    const b = mockClient.getLastBuilder("quotes")!;
    expect(b.insert).toHaveBeenCalledTimes(1);
    const insertedArg = b.insert.mock.calls[0][0];
    expect(Array.isArray(insertedArg)).toBe(true);
    const row = insertedArg[0];
    expect(row).toMatchObject({
      request_id: "req1",
      created_by: "user1",
      modified_by: "user1",
      ai_generated_data: { a: 1 },
      template_id: "tpl1",
      version: 1,
      status: "brouillon"
    });
    expect(b.select).toHaveBeenCalled();
    expect(b.single).toHaveBeenCalled();
  });

  test("createQuote throws with supabase error message", async () => {
    mockClient.setFromResult("quotes", { data: null, error: { message: "boom" } });
    await expect(db.createQuote({ request_id: "r", created_by: "u" }))
      .rejects.toThrow("Failed to create quote: boom");
  });

  test("getQuoteWithItems returns data", async () => {
    const fake: any = { id: "q1", items: [] };
    mockClient.setFromResult("quotes_with_items", { data: fake, error: null });
    const res = await db.getQuoteWithItems("q1");
    expect(res).toEqual(fake);
    const b = mockClient.getLastBuilder("quotes_with_items")!;
    expect(b.select).toHaveBeenCalledWith("*");
    expect(b.eq).toHaveBeenCalledWith("id", "q1");
    expect(b.single).toHaveBeenCalled();
  });

  test("getQuoteWithItems returns null when error code PGRST116", async () => {
    mockClient.setFromResult("quotes_with_items", { data: null, error: { code: "PGRST116", message: "No rows" } });
    await expect(db.getQuoteWithItems("nope")).resolves.toBeNull();
  });

  test("getQuoteWithItems throws other errors", async () => {
    mockClient.setFromResult("quotes_with_items", { data: null, error: { code: "XX", message: "db fail" } });
    await expect(db.getQuoteWithItems("q1")).rejects.toThrow("Failed to get quote: db fail");
  });

  test("updateQuote merges updates, sets modified_by and updated_at", async () => {
    const nowISO = new Date().toISOString();
    const updated: any = { id: "q1", foo: "bar" };
    mockClient.setFromResult("quotes", { data: updated, error: null });
    const res = await db.updateQuote("q1", { foo: "bar" } as any, "u2");
    expect(res).toBe(updated);
    const b = mockClient.getLastBuilder("quotes")!;
    expect(b.update).toHaveBeenCalledTimes(1);
    const arg = b.update.mock.calls[0][0];
    expect(arg.foo).toBe("bar");
    expect(arg.modified_by).toBe("u2");
    expect(arg.updated_at).toBe(nowISO);
    expect(b.eq).toHaveBeenCalledWith("id", "q1");
    expect(b.select).toHaveBeenCalled();
    expect(b.single).toHaveBeenCalled();
  });

  test("getQuotesByRequestId returns list ordered desc; empty fallback", async () => {
    mockClient.setFromResult("quotes", { data: null, error: null });
    const res = await db.getQuotesByRequestId("reqX");
    expect(res).toEqual([]);
    const b = mockClient.getLastBuilder("quotes")!;
    expect(b.select).toHaveBeenCalledWith("*");
    expect(b.eq).toHaveBeenCalledWith("request_id", "reqX");
    expect(b.order).toHaveBeenCalledWith("version", { ascending: false });
  });

  test("getQuotesByRequestId throws on error", async () => {
    mockClient.setFromResult("quotes", { data: null, error: { message: "oops" } });
    await expect(db.getQuotesByRequestId("reqX")).rejects.toThrow("Failed to get quotes: oops");
  });
});

describe("QUOTE ITEMS", () => {
  test("createQuoteItem inserts with onConflict and returns item", async () => {
    const item: any = { id: "i1", quote_id: "q1" };
    mockClient.setFromResult("quote_items", { data: item, error: null });
    const res = await db.createQuoteItem({ quote_id: "q1", description: "d", order_index: 0 } as any);
    expect(res).toBe(item);
    const b = mockClient.getLastBuilder("quote_items")!;
    expect(b.insert).toHaveBeenCalledTimes(1);
    expect(b.select).toHaveBeenCalled();
    expect(b.single).toHaveBeenCalled();
  });

  test("createQuoteItem throws on error", async () => {
    mockClient.setFromResult("quote_items", { data: null, error: { message: "nope" } });
    await expect(db.createQuoteItem({} as any)).rejects.toThrow("Failed to create quote item: nope");
  });

  test("updateQuoteItem sets updated_at and returns item", async () => {
    const nowISO = new Date().toISOString();
    mockClient.setFromResult("quote_items", { data: { id: "i1" }, error: null });
    const res = await db.updateQuoteItem("i1", { description: "x" } as any);
    expect(res).toEqual({ id: "i1" });
    const b = mockClient.getLastBuilder("quote_items")!;
    const arg = b.update.mock.calls[0][0];
    expect(arg.description).toBe("x");
    expect(arg.updated_at).toBe(nowISO);
    expect(b.eq).toHaveBeenCalledWith("id", "i1");
  });

  test("deleteQuoteItem deletes by id", async () => {
    mockClient.setFromResult("quote_items", { data: null, error: null });
    await expect(db.deleteQuoteItem("i1")).resolves.toBeUndefined();
    const b = mockClient.getLastBuilder("quote_items")!;
    expect(b.delete).toHaveBeenCalled();
    expect(b.eq).toHaveBeenCalledWith("id", "i1");
  });

  test("deleteQuoteItem throws on error", async () => {
    mockClient.setFromResult("quote_items", { data: null, error: { message: "fail del" } });
    await expect(db.deleteQuoteItem("i1")).rejects.toThrow("Failed to delete quote item: fail del");
  });

  test("getQuoteItems returns ordered list or []", async () => {
    mockClient.setFromResult("quote_items", { data: [{ id: "i1" }], error: null });
    const res = await db.getQuoteItems("q1");
    expect(res).toEqual([{ id: "i1" }]);
    const b = mockClient.getLastBuilder("quote_items")!;
    expect(b.select).toHaveBeenCalledWith("*");
    expect(b.eq).toHaveBeenCalledWith("quote_id", "q1");
    expect(b.order).toHaveBeenCalledWith("order_index");
  });

  test("getQuoteItems throws on error", async () => {
    mockClient.setFromResult("quote_items", { data: null, error: { message: "err" } });
    await expect(db.getQuoteItems("q1")).rejects.toThrow("Failed to get quote items: err");
  });

  test("bulkCreateQuoteItems inserts batch", async () => {
    const items: any = [{ id: "a" }, { id: "b" }];
    mockClient.setFromResult("quote_items", { data: items, error: null });
    const res = await db.bulkCreateQuoteItems([{} as any]);
    expect(res).toBe(items);
    const b = mockClient.getLastBuilder("quote_items")!;
    expect(b.insert).toHaveBeenCalled();
    expect(b.select).toHaveBeenCalled();
  });

  test("bulkCreateQuoteItems throws on error", async () => {
    mockClient.setFromResult("quote_items", { data: null, error: { message: "bad insert" } });
    await expect(db.bulkCreateQuoteItems([] as any)).rejects.toThrow("Failed to create quote items: bad insert");
  });
});

describe("TEMPLATES", () => {
  test("createTemplate applies defaults", async () => {
    const tpl: any = { id: "t1" };
    mockClient.setFromResult("templates", { data: tpl, error: null });
    const res = await db.createTemplate({
      name: "N",
      category: "cat",
      items: [],
      created_by: "u"
    });
    expect(res).toBe(tpl);
    const b = mockClient.getLastBuilder("templates")!;
    const inserted = b.insert.mock.calls[0][0][0];
    expect(inserted.default_margin).toBe(15);
    expect(inserted.complexity_level).toBe(2);
    expect(inserted.is_public).toBe(false);
  });

  test("createTemplate throws on error", async () => {
    mockClient.setFromResult("templates", { data: null, error: { message: "rip" } });
    await expect(db.createTemplate({ name: "N", category: "C", items: [], created_by: "u" } as any))
      .rejects.toThrow("Failed to create template: rip");
  });

  test("getTemplatesByUser calls RPC without parameters", async () => {
    mockClient.setRpcResult("get_user_templates", { data: [], error: null });
    const res = await db.getTemplatesByUser();
    expect(res).toEqual([]);
    expect(mockClient.rpc).toHaveBeenCalledWith("get_user_templates");
  });

  test("getTemplatesByUser throws on error", async () => {
    mockClient.setRpcResult("get_user_templates", { data: null, error: { message: "t err" } });
    await expect(db.getTemplatesByUser()).rejects.toThrow("Failed to get templates: t err");
  });

  test("getTemplatesByCategory calls RPC with category parameter", async () => {
    mockClient.setRpcResult("get_templates_by_category", { data: [{ id: "t" }], error: null });
    const res = await db.getTemplatesByCategory("cat");
    expect(res).toEqual([{ id: "t" }]);
    expect(mockClient.rpc).toHaveBeenCalledWith("get_templates_by_category", { p_category: "cat" });
  });

  test("getTemplatesByCategory without userId calls RPC with category parameter", async () => {
    mockClient.setRpcResult("get_templates_by_category", { data: [], error: null });
    await db.getTemplatesByCategory("cat");
    expect(mockClient.rpc).toHaveBeenCalledWith("get_templates_by_category", { p_category: "cat" });
  });

  test("getTemplatesByCategory throws on error", async () => {
    mockClient.setRpcResult("get_templates_by_category", { data: null, error: { message: "fail gc" } });
    await expect(db.getTemplatesByCategory("x")).rejects.toThrow("Failed to get templates by category: fail gc");
  });

  test("incrementTemplateUsage calls RPC and throws on error", async () => {
    mockClient.setRpcResult("increment_template_usage", { data: null, error: null });
    await expect(db.incrementTemplateUsage("tid")).resolves.toBeUndefined();
    expect(mockClient.rpc).toHaveBeenCalledWith("increment_template_usage", { template_uuid: "tid" });

    mockClient.setRpcResult("increment_template_usage", { data: null, error: { message: "rpc-fail" } });
    await expect(db.incrementTemplateUsage("tid")).rejects.toThrow("Failed to increment template usage: rpc-fail");
  });
});

describe("PRODUCTS CATALOG", () => {
  test("searchProducts builds query and returns [] on null data", async () => {
    mockClient.setFromResult("products_catalog", { data: null, error: null });
    const res = await db.searchProducts("sprocket");
    expect(res).toEqual([]);
    const b = mockClient.getLastBuilder("products_catalog")!;
    expect(b.textSearch).toHaveBeenCalledWith("name", "sprocket", { type: "websearch", config: "french" });
    expect(b.eq).toHaveBeenCalledWith("is_active", true);
    expect(b.order).toHaveBeenCalledWith("current_price");
    expect(b.limit).toHaveBeenCalledWith(20);
  });

  test("searchProducts filters by category when provided", async () => {
    mockClient.setFromResult("products_catalog", { data: [{ id: "p1" }], error: null });
    const res = await db.searchProducts("bolt", "hardware");
    expect(res).toEqual([{ id: "p1" }]);
    const b = mockClient.getLastBuilder("products_catalog")!;
    expect(b.eq).toHaveBeenCalledWith("category", "hardware");
  });

  test("searchProducts throws on error", async () => {
    mockClient.setFromResult("products_catalog", { data: null, error: { message: "search err" } });
    await expect(db.searchProducts("x")).rejects.toThrow("Failed to search products: search err");
  });

  test("getProductsByCategory enforces active and sorts", async () => {
    mockClient.setFromResult("products_catalog", { data: [{ id: "1" }], error: null });
    const res = await db.getProductsByCategory("tools");
    expect(res).toEqual([{ id: "1" }]);
    const b = mockClient.getLastBuilder("products_catalog")!;
    expect(b.eq).toHaveBeenCalledWith("category", "tools");
    expect(b.eq).toHaveBeenCalledWith("is_active", true);
    expect(b.order).toHaveBeenCalledWith("current_price");
  });

  test("getProductsByCategory throws on error", async () => {
    mockClient.setFromResult("products_catalog", { data: null, error: { message: "pc err" } });
    await expect(db.getProductsByCategory("tools")).rejects.toThrow("Failed to get products by category: pc err");
  });
});

describe("QUOTE HISTORY", () => {
  test("getQuoteHistory selects joined profiles and orders by created_at desc", async () => {
    mockClient.setFromResult("quote_history", { data: [], error: null });
    const res = await db.getQuoteHistory("q1");
    expect(res).toEqual([]);
    const b = mockClient.getLastBuilder("quote_history")!;
    // Validate select string shape
    const sel = b.select.mock.calls[0][0] as string;
    expect(sel).toContain("profiles!quote_history_modified_by_fkey");
    expect(b.eq).toHaveBeenCalledWith("quote_id", "q1");
    expect(b.order).toHaveBeenCalledWith("created_at", { ascending: false });
  });

  test("getQuoteHistory throws on error", async () => {
    mockClient.setFromResult("quote_history", { data: null, error: { message: "qh err" } });
    await expect(db.getQuoteHistory("q1")).rejects.toThrow("Failed to get quote history: qh err");
  });
});

describe("NOTIFICATIONS", () => {
  test("createNotification applies defaults and returns record", async () => {
    const notif: any = { id: "n1" };
    mockClient.setFromResult("notifications", { data: notif, error: null });
    const res = await db.createNotification({
      quote_id: "q1",
      type: "email",
      recipient_email: "<EMAIL>"
    });
    expect(res).toBe(notif);
    const b = mockClient.getLastBuilder("notifications")!;
    const inserted = b.insert.mock.calls[0][0][0];
    expect(inserted.status).toBe("pending");
    expect(inserted.retry_count).toBe(0);
    expect(inserted.max_retries).toBe(3);
  });

  test("createNotification throws on error", async () => {
    mockClient.setFromResult("notifications", { data: null, error: { message: "notif err" } });
    await expect(db.createNotification({ type: "email", recipient_email: "a@b.c" } as any))
      .rejects.toThrow("Failed to create notification: notif err");
  });

  test("scheduleQuoteReminder uses defaults and returns rpc data", async () => {
    mockClient.setRpcResult("schedule_quote_reminder", { data: "ok", error: null });
    const res = await db.scheduleQuoteReminder("q1");
    expect(res).toBe("ok");
    expect(mockClient.rpc).toHaveBeenCalledWith("schedule_quote_reminder", {
      quote_uuid: "q1",
      days_delay: 7,
      reminder_type: "relance"
    });
  });

  test("scheduleQuoteReminder throws on error", async () => {
    mockClient.setRpcResult("schedule_quote_reminder", { data: null, error: { message: "rpc err" } });
    await expect(db.scheduleQuoteReminder("q1", 3, "custom")).rejects.toThrow("Failed to schedule reminder: rpc err");
  });
});

describe("INVOICES", () => {
  test("convertQuoteToInvoice returns invoice id from RPC", async () => {
    mockClient.setRpcResult("convert_quote_to_invoice", { data: "inv1", error: null });
    const res = await db.convertQuoteToInvoice("q1");
    expect(res).toBe("inv1");
    expect(mockClient.rpc).toHaveBeenCalledWith("convert_quote_to_invoice", { quote_uuid: "q1" });
  });

  test("convertQuoteToInvoice throws on error", async () => {
    mockClient.setRpcResult("convert_quote_to_invoice", { data: null, error: { message: "conv err" } });
    await expect(db.convertQuoteToInvoice("q1")).rejects.toThrow("Failed to convert quote to invoice: conv err");
  });

  test("getInvoicesByUser fetches and orders invoices", async () => {
    mockClient.setFromResult("invoices", { data: [{ id: "i1" }], error: null });
    const res = await db.getInvoicesByUser("u1");
    expect(res).toEqual([{ id: "i1" }]);
    const b = mockClient.getLastBuilder("invoices")!;
    expect(b.select).toHaveBeenCalledWith("*");
    expect(b.eq).toHaveBeenCalledWith("created_by", "u1");
    expect(b.order).toHaveBeenCalledWith("created_at", { ascending: false });
  });

  test("getInvoicesByUser returns [] on null data, throws on error", async () => {
    mockClient.setFromResult("invoices", { data: null, error: null });
    await expect(db.getInvoicesByUser("u1")).resolves.toEqual([]);
    mockClient.setFromResult("invoices", { data: null, error: { message: "inv err" } });
    await expect(db.getInvoicesByUser("u1")).rejects.toThrow("Failed to get invoices: inv err");
  });

  test("updateInvoiceStatus sets paid_at when status 'payée' and paymentData provided", async () => {
    // Mock current invoice fetch
    mockClient.setFromResult("invoices", { data: { status: "brouillon" }, error: null });
    const res = await db.updateInvoiceStatus("i1", "payée", { payment_reference: "ref1" });
    expect(res).toEqual({ id: "i1", status: "payée" });
    const iso = new Date().toISOString();
    const builders = mockClient.builders["invoices"] || [];
    expect(builders.length).toBe(2); // One for select, one for update
    const selectBuilder = builders[0];
    const updateBuilder = builders[1];
    // Check select query
    expect(selectBuilder.select).toHaveBeenCalledWith("status");
    expect(selectBuilder.eq).toHaveBeenCalledWith("id", "i1");
    expect(selectBuilder.single).toHaveBeenCalled();
    // Check update query
    const arg = updateBuilder.update.mock.calls[0][0];
    expect(arg.status).toBe("payée");
    expect(arg.payment_reference).toBe("ref1");
    expect(arg.paid_at).toBe(iso);
    expect(updateBuilder.eq).toHaveBeenCalledWith("id", "i1");
    expect(updateBuilder.select).toHaveBeenCalled();
    expect(updateBuilder.single).toHaveBeenCalled();
  });

  test("updateInvoiceStatus sets sent_at when status 'envoyée'", async () => {
    mockClient.setFromResult("invoices", { data: { id: "i2", status: "envoyée" }, error: null });
    const res = await db.updateInvoiceStatus("i2", "envoyée");
    expect(res).toEqual({ id: "i2", status: "envoyée" });
    const iso = new Date().toISOString();
    const arg = mockClient.getLastBuilder("invoices")!.update.mock.calls[0][0];
    expect(arg.sent_at).toBe(iso);
  });

  test("updateInvoiceStatus throws on error", async () => {
    mockClient.setFromResult("invoices", { data: null, error: { message: "upd err" } });
    await expect(db.updateInvoiceStatus("i1", "x")).rejects.toThrow("Failed to update invoice: upd err");
  });
});

describe("DASHBOARD STATS", () => {
  test("getDashboardStats returns data", async () => {
    const stats: any = { pending_requests: 1, analyzing_requests: 2, draft_quotes: 3, sent_quotes: 4, accepted_quotes: 5, total_accepted_amount: 6, avg_quote_amount: 7 };
    mockClient.setFromResult("dashboard_stats", { data: stats, error: null });
    const res = await db.getDashboardStats();
    expect(res).toEqual(stats);
    const b = mockClient.getLastBuilder("dashboard_stats")!;
    expect(b.select).toHaveBeenCalledWith("*");
    expect(b.single).toHaveBeenCalled();
  });

  test("getDashboardStats returns zeros when PGRST116", async () => {
    mockClient.setFromResult("dashboard_stats", { data: null, error: { code: "PGRST116", message: "no row" } });
    const res = await db.getDashboardStats();
    expect(res).toEqual({ pending_requests: 0, analyzing_requests: 0, draft_quotes: 0, sent_quotes: 0, accepted_quotes: 0, total_accepted_amount: 0, avg_quote_amount: 0 });
  });

  test("getDashboardStats throws other errors", async () => {
    mockClient.setFromResult("dashboard_stats", { data: null, error: { code: "XX", message: "bad" } });
    await expect(db.getDashboardStats()).rejects.toThrow("Failed to get dashboard stats: bad");
  });
});

describe("UTILITY: createQuoteFromAI", () => {
  test("creates quote from AI data via RPC", async () => {
    const expectedQuote = { id: "qAI", items: [{ id: "i1" }] };
    mockClient.setRpcResult("create_quote_and_items_from_ai", { data: expectedQuote, error: null });

    const aiData = {
      items: [
        { category: "cat", label: "L", description: "D", quantity: 2, unit: "u", unitPrice: 10, material_cost: 1, labor_cost: 2 },
        { category: "cat2", description: "D2", unitPrice: 0 }
      ],
      summary: "Test summary",
      aiEstimateTotal: 100,
      aiConfidence: 0.8,
      usedProvider: "test"
    };

    const res = await db.createQuoteFromAI("req123", "userX", aiData, "tplZ");
    expect(res).toEqual(expectedQuote);
    expect(mockClient.rpc).toHaveBeenCalledWith("create_quote_and_items_from_ai", {
      request_uuid: "req123",
      created_by_uuid: "userX",
      ai_payload: aiData,
      template_uuid: "tplZ"
    });
  });

  test("createQuoteFromAI throws on RPC error", async () => {
    mockClient.setRpcResult("create_quote_and_items_from_ai", { data: null, error: { message: "rpc error" } });
    const aiData = {
      items: [],
      summary: "Test",
      aiEstimateTotal: 0,
      aiConfidence: 1,
      usedProvider: "test"
    };
    await expect(db.createQuoteFromAI("r", "u", aiData)).rejects.toThrow("Failed to create quote from AI: rpc error");
  });
});

describe("UTILITY: createQuoteFromTemplate", () => {
  test("creates from template, increments usage, applies surface multiplier and custom items", async () => {
    // Template fetch
    const template = {
      id: "tpl1",
      items: [
        { category: "C", description: "m2 item", quantity: 10, unit: "m2", unit_price: 5, material_cost: 1, labor_cost: 1, complexity_factor: 1, is_optional: false }
      ]
    };
    mockClient.setFromResult("templates", { data: template as any, error: null });
    // RPC increment
    mockClient.setRpcResult("increment_template_usage", { data: null, error: null });
    // Quote creation
    mockClient.setFromResult("quotes", { data: { id: "qT" }, error: null });
    // Items insertion
    mockClient.setFromResult("quote_items", { data: [{ id: "iX" }], error: null });
    // Final fetch
    mockClient.setFromResult("quotes_with_items", { data: { id: "qT", items: [{ id: "iX" }] }, error: null });

    const res = await db.createQuoteFromTemplate("reqT", "userT", "tpl1", {
      surfaceMultiplier: 3,
      customItems: [{ category: "C2", description: "custom", quantity: 1, unit: "u", unit_price: 10, material_cost: 0, labor_cost: 0, complexity_factor: 1, is_optional: true }]
    });

    expect(res).toEqual({ id: "qT", items: [{ id: "iX" }] });

    // Validate template fetch and RPC
    expect(mockClient.from).toHaveBeenCalledWith("templates");
    expect(mockClient.rpc).toHaveBeenCalledWith("increment_template_usage", { template_uuid: "tpl1" });

    const bQI = mockClient.getLastBuilder("quote_items")!;
    const inserted = bQI.insert.mock.calls[0][0] as any[];
    // First item quantity should be multiplied (unit m2)
    expect(inserted[0]).toMatchObject({ unit: "m2", quantity: 30, category: "C" });
    // Custom item appended
    expect(inserted[1]).toMatchObject({ category: "C2", description: "custom", is_optional: true, ai_suggested: false });
  });

  test("createQuoteFromTemplate throws when template fetch fails", async () => {
    mockClient.setFromResult("templates", { data: null, error: { message: "tpl err" } });
    await expect(db.createQuoteFromTemplate("r", "u", "tplX")).rejects.toThrow("Failed to get template: tpl err");
  });
});
