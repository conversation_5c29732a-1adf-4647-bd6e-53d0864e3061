import 'server-only'
import { createServerClient } from '@supabase/ssr'
import { createClient } from '@supabase/supabase-js'
import { cookies } from 'next/headers'
import type {
  Database,
  QuoteRecord,
  QuoteItemRecord,
  TemplateRecord,
  ProductCatalogRecord,
  QuoteHistoryRecord,
  NotificationRecord,
  InvoiceRecord,
  QuoteWithItems,
  DashboardStats,
  QuoteItemTemplate,
  AiGeneratedData
} from './types'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!

export async function createServerClientForRequest() {
  if (!supabaseUrl || !supabaseAnonKey) throw new Error('Supabase env vars are missing')
  const store = await cookies()
  return createServerClient<Database>(supabaseUrl, supabaseAnonKey, {
    cookies: {
      get(name: string) { return (store as any).get(name)?.value },
      set(name: string, value: string, options: any) { (store as any).set(name, value, options) },
      remove(name: string, options: any) { (store as any).set(name, '', { ...options, expires: new Date(0) }) },
    },
  })
}

export function createServiceRoleClient() {
  if (!supabaseUrl || !supabaseServiceKey) throw new Error('Service role env vars are missing')
  return createClient<Database>(supabaseUrl, supabaseServiceKey, { auth: { persistSession: false } })
}

// === QUOTES ===

export async function createQuote(data: {
  request_id: string
  created_by: string
  ai_generated_data?: AiGeneratedData
  template_id?: string
}): Promise<QuoteRecord> {
  const supabase = await createServerClientForRequest()
  const { data: quote, error } = await supabase
    .from('quotes')
    .insert([{
      request_id: data.request_id,
      created_by: data.created_by,
      modified_by: data.created_by,
      ai_generated_data: data.ai_generated_data,
      template_id: data.template_id,
      version: 1,
      status: 'brouillon'
    }])
    .select()
    .single()

  if (error) throw new Error(`Failed to create quote: ${error.message}`)
  if (!quote) throw new Error('Failed to create quote, no data returned')
  return quote as QuoteRecord
}

export async function getQuoteWithItems(quoteId: string): Promise<QuoteWithItems | null> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('quotes_with_items')
    .select('*')
    .eq('id', quoteId)
    .single()

  if (error) {
    if (error.code === 'PGRST116') return null
    throw new Error(`Failed to get quote: ${error.message}`)
  }

  return data as QuoteWithItems | null
}

export async function updateQuote(
  quoteId: string,
  updates: Partial<QuoteRecord>,
  modifiedBy: string
): Promise<QuoteRecord> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('quotes')
    .update({ ...updates, modified_by: modifiedBy, updated_at: new Date().toISOString() })
    .eq('id', quoteId)
    .select()
    .single()

  if (error) throw new Error(`Failed to update quote: ${error.message}`)
  if (!data) throw new Error(`Quote not found after update: ${quoteId}`)
  return data as QuoteRecord
}

export async function getQuotesByRequestId(requestId: string): Promise<QuoteRecord[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('quotes')
    .select('*')
    .eq('request_id', requestId)
    .order('version', { ascending: false })

  if (error) throw new Error(`Failed to get quotes: ${error.message}`)
  return (data as QuoteRecord[]) || []
}

// === QUOTE ITEMS ===

export async function createQuoteItem(data: Omit<QuoteItemRecord, 'id' | 'created_at' | 'updated_at' | 'total_price'>): Promise<QuoteItemRecord> {
  const supabase = await createServerClientForRequest()
  const { data: item, error } = await supabase
    .from('quote_items')
    .insert(data)
    .select()
    .single()

  if (error) throw new Error(`Failed to create quote item: ${error.message}`)
  if (!item) throw new Error('Failed to create quote item, no data returned')
  return item as QuoteItemRecord
}

export async function updateQuoteItem(
  itemId: string,
  updates: Partial<QuoteItemRecord>
): Promise<QuoteItemRecord> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('quote_items')
    .update({ ...updates, updated_at: new Date().toISOString() })
    .eq('id', itemId)
    .select()
    .single()

  if (error) throw new Error(`Failed to update quote item: ${error.message}`)
  if (!data) throw new Error(`Item not found after update: ${itemId}`)
  return data as QuoteItemRecord
}

export async function deleteQuoteItem(itemId: string): Promise<void> {
  const supabase = await createServerClientForRequest()
  const { error } = await supabase
    .from('quote_items')
    .delete()
    .eq('id', itemId)

  if (error) throw new Error(`Failed to delete quote item: ${error.message}`)
}

export async function getQuoteItems(quoteId: string): Promise<QuoteItemRecord[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('quote_items')
    .select('*')
    .eq('quote_id', quoteId)
    .order('order_index')

  if (error) throw new Error(`Failed to get quote items: ${error.message}`)
  return (data as QuoteItemRecord[]) || []
}

export async function bulkCreateQuoteItems(items: Omit<QuoteItemRecord, 'id' | 'created_at' | 'updated_at' | 'total_price'>[]): Promise<QuoteItemRecord[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('quote_items')
    .insert(items)
    .select()

  if (error) throw new Error(`Failed to create quote items: ${error.message}`)
  return (data as QuoteItemRecord[]) || []
}

// === TEMPLATES ===

export async function createTemplate(data: {
  name: string
  description?: string
  category: string
  subcategory?: string
  items: QuoteItemTemplate[]
  created_by: string
  default_margin?: number
  estimated_duration_hours?: number
  complexity_level?: number
  is_public?: boolean
  tags?: string[]
  notes?: string
}): Promise<TemplateRecord> {
  const supabase = await createServerClientForRequest()
  const { data: template, error } = await supabase
    .from('templates')
    .insert([{
      ...data,
      default_margin: data.default_margin ?? 15,
      complexity_level: data.complexity_level ?? 2,
      is_public: data.is_public ?? false
    }])
    .select()
    .single()

  if (error) throw new Error(`Failed to create template: ${error.message}`)
  if (!template) throw new Error('Failed to create template, no data returned')
  return template as TemplateRecord
}

export async function getTemplatesByUser(): Promise<TemplateRecord[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .rpc('get_user_templates')

  if (error) throw new Error(`Failed to get templates: ${error.message}`)
  return (data as TemplateRecord[]) || []
}

export async function getTemplatesByCategory(category: string): Promise<TemplateRecord[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .rpc('get_templates_by_category', { p_category: category })

  if (error) throw new Error(`Failed to get templates by category: ${error.message}`)
  return (data as TemplateRecord[]) || []
}

export async function incrementTemplateUsage(templateId: string): Promise<void> {
  const supabase = await createServerClientForRequest()
  const { error } = await supabase.rpc('increment_template_usage', {
    template_uuid: templateId
  })

  if (error) throw new Error(`Failed to increment template usage: ${error.message}`)
}

// === PRODUCTS CATALOG ===

export async function searchProducts(query: string, category?: string): Promise<ProductCatalogRecord[]> {
  const supabase = await createServerClientForRequest()
  const q = query?.trim() ?? ''
  if (!q) return []

  let dbQuery = supabase
    .from('products_catalog')
    .select('*')
    .eq('is_active', true)
    .textSearch('name', q, { type: 'websearch', config: 'french' })

  if (category) {
    dbQuery = dbQuery.eq('category', category)
  }

  const { data, error } = await dbQuery
    .order('current_price')
    .limit(20)

  if (error) throw new Error(`Failed to search products: ${error.message}`)
  return (data as ProductCatalogRecord[]) || []
}

export async function getProductsByCategory(category: string): Promise<ProductCatalogRecord[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('products_catalog')
    .select('*')
    .eq('category', category)
    .eq('is_active', true)
    .order('current_price')

  if (error) throw new Error(`Failed to get products by category: ${error.message}`)
  return (data as ProductCatalogRecord[]) || []
}

// === QUOTE HISTORY ===

export async function getQuoteHistory(quoteId: string): Promise<QuoteHistoryRecord[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('quote_history')
    .select(`
      *,
      profiles!quote_history_modified_by_fkey (
        id,
        name
      )
    `)
    .eq('quote_id', quoteId)
    .order('created_at', { ascending: false })

  if (error) throw new Error(`Failed to get quote history: ${error.message}`)
  return (data as QuoteHistoryRecord[]) || []
}

// === NOTIFICATIONS ===

export async function createNotification(data: {
  request_id?: string
  quote_id?: string
  type: string
  recipient_email: string
  recipient_name?: string
  subject?: string
  message?: string
  scheduled_at?: string
}): Promise<NotificationRecord> {
  const supabase = await createServerClientForRequest()
  const { data: notification, error } = await supabase
    .from('notifications')
    .insert([{
      ...data,
      status: 'pending',
      retry_count: 0,
      max_retries: 3
    }])
    .select()
    .single()

  if (error) throw new Error(`Failed to create notification: ${error.message}`)
  if (!notification) throw new Error('Failed to create notification, no data returned')
  return notification as NotificationRecord
}

export async function scheduleQuoteReminder(
  quoteId: string,
  daysDelay: number = 7,
  reminderType: string = 'relance'
): Promise<string> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase.rpc('schedule_quote_reminder', {
    quote_uuid: quoteId,
    days_delay: daysDelay,
    reminder_type: reminderType
  })

  if (error) throw new Error(`Failed to schedule reminder: ${error.message}`)
  return data as string
}

// === INVOICES ===

export async function convertQuoteToInvoice(quoteId: string): Promise<string> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase.rpc('convert_quote_to_invoice', {
    quote_uuid: quoteId
  })

  if (error) throw new Error(`Failed to convert quote to invoice: ${error.message}`)
  return data as string
}

export async function getInvoicesByUser(userId: string): Promise<InvoiceRecord[]> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase
    .from('invoices')
    .select('*')
    .eq('created_by', userId)
    .order('created_at', { ascending: false })

  if (error) throw new Error(`Failed to get invoices: ${error.message}`)
  return (data as InvoiceRecord[]) || []
}

export async function updateInvoiceStatus(
  invoiceId: string,
  status: 'brouillon' | 'envoyée' | 'payée' | 'annulée',
  paymentData?: {
    payment_date?: string
    payment_method?: string
    payment_reference?: string
  }
): Promise<InvoiceRecord> {
  const supabase = await createServerClientForRequest()

  // Get current invoice to validate status transitions
  const { data: currentInvoice, error: fetchError } = await supabase
    .from('invoices')
    .select('status')
    .eq('id', invoiceId)
    .single()

  if (fetchError) throw new Error(`Failed to fetch invoice: ${fetchError.message}`)
  if (!currentInvoice) throw new Error(`Invoice not found: ${invoiceId}`)

  // Validate status transitions
  if (currentInvoice.status === 'annulée' && status === 'payée') {
    throw new Error('Cannot change status from annulée to payée')
  }

  const updates: Partial<InvoiceRecord> = { status }

  if (paymentData) {
    Object.assign(updates, paymentData)
  }

  // Set paid_at when status is 'payée', but avoid overwriting existing values
  if (status === 'payée' && !updates.paid_at && !paymentData?.payment_date) {
    updates.paid_at = new Date().toISOString()
  }

  if (status === 'envoyée') {
    updates.sent_at = new Date().toISOString()
  }

  const { data, error } = await supabase
    .from('invoices')
    .update(updates)
    .eq('id', invoiceId)
    .select()
    .single()

  if (error) throw new Error(`Failed to update invoice: ${error.message}`)
  if (!data) throw new Error(`Invoice not found after update: ${invoiceId}`)
  return data as InvoiceRecord
}

// === DASHBOARD STATS ===
export async function getDashboardStats(): Promise<DashboardStats> {
  const supabase = await createServerClientForRequest();

  const { data, error } = await supabase
    .from("dashboard_stats")
    .select("*")
    .single();

  if (error) {
    if (error.code === "PGRST116") {
      // No rows found - return zeros
      return {
        pending_requests: 0,
        analyzing_requests: 0,
        draft_quotes: 0,
        sent_quotes: 0,
        accepted_quotes: 0,
        total_accepted_amount: 0,
        avg_quote_amount: 0
      };
    }
    throw new Error(`Failed to get dashboard stats: ${error.message}`);
  }

  return data;
}

// === REQUESTS ===

export async function assignRequestToPro(
  requestId: string,
  proUserId: string
): Promise<void> {
  const supabase = createServiceRoleClient()
  const { error } = await supabase
    .from('requests')
    .update({
      status: 'en_cours',
      assigned_to: proUserId,
      updated_at: new Date().toISOString()
    })
    .eq('id', requestId)

  if (error) throw new Error(`Failed to assign request: ${error.message}`)
}

export async function unassignRequest(requestId: string): Promise<void> {
  const supabase = createServiceRoleClient()
  const { error } = await supabase
    .from('requests')
    .update({
      assigned_to: null,
      updated_at: new Date().toISOString()
    })
    .eq('id', requestId)

  if (error) throw new Error(`Failed to unassign request: ${error.message}`)
}

// === UTILITY FUNCTIONS ===

export async function createQuoteFromAI(
  requestId: string,
  userId: string,
  aiQuoteData: AiGeneratedData,
  templateId?: string
): Promise<QuoteWithItems> {
  const supabase = await createServerClientForRequest()
  const { data, error } = await supabase.rpc('create_quote_and_items_from_ai', {
    request_uuid: requestId,
    created_by_uuid: userId,
    ai_payload: aiQuoteData,
    template_uuid: templateId ?? null
  })
  if (error) throw new Error(`Failed to create quote from AI: ${error.message}`)
  return data as QuoteWithItems
}

export async function createQuoteFromTemplate(
  requestId: string,
  userId: string,
  templateId: string,
  modifications?: {
    surfaceMultiplier?: number
    customItems?: QuoteItemTemplate[]
  }
): Promise<QuoteWithItems> {
  // Get template
  const supabase = await createServerClientForRequest()
  const { data: template, error: templateError } = await supabase
    .from('templates')
    .select('*')
    .eq('id', templateId)
    .single()

  if (templateError) throw new Error(`Failed to get template: ${templateError.message}`)
  if (!template) throw new Error(`Template not found: ${templateId}`)

  // Increment usage count
  await incrementTemplateUsage(templateId)

  // Create quote
  const quote = await createQuote({
    request_id: requestId,
    created_by: userId,
    template_id: templateId
  })

  // Create items from template
  let templateItems = template.items || []

  // Apply surface multiplier if provided
  if (modifications?.surfaceMultiplier != null && modifications.surfaceMultiplier !== 1) {
    templateItems = templateItems.map((item: QuoteItemTemplate, index: number) => ({
      ...item,
      quantity: item.unit === 'm2' ? item.quantity * modifications.surfaceMultiplier! : item.quantity
    }))
  }

  // Add custom items if provided
  if (modifications?.customItems) {
    templateItems = [...templateItems, ...modifications.customItems]
  }

  const items = templateItems.map((item: QuoteItemTemplate, index: number) => ({
    quote_id: quote.id,
    category: item.category,
    subcategory: item.subcategory,
    description: item.description,
    quantity: item.quantity,
    unit: item.unit,
    unit_price: item.unit_price,
    material_cost: item.material_cost,
    labor_cost: item.labor_cost,
    complexity_factor: item.complexity_factor,
    order_index: index,
    is_optional: item.is_optional,
    supplier: item.supplier,
    notes: item.notes,
    ai_suggested: false
  }))

  if (items.length > 0) {
    await bulkCreateQuoteItems(items)
  }

  // Get the complete quote with items
  const completeQuote = await getQuoteWithItems(quote.id)
  if (!completeQuote) {
    throw new Error('Failed to retrieve created quote')
  }

  return completeQuote
}
