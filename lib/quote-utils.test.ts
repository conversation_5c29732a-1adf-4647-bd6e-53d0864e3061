/*
Test framework: Vitest
- Uses Vitest for testing with global describe, it, expect functions
- All mocking and utilities use Vitest APIs (vi.*)
*/

import {
  convertLegacyQuoteToDatabase,
  convertDatabaseQuoteToLegacy,
  calculateQuoteTotals,
  estimateProjectDuration,
  generateBaseQuoteItems,
  validateQuote,
  compareQuoteVersions,
} from './quote-utils'
import { DEFAULT_MARGIN } from './quote-constants'

// Import types if available; otherwise declare minimal inline types for tests to compile.
let typesAvailable = true;
try {
  // Dynamic require to avoid breaking if types path differs at runtime
  // eslint-disable-next-line @typescript-eslint/no-var-requires
  require('./types');
} catch {
  typesAvailable = false;
}

// Minimal local type shims to keep tests type-safe enough if ./types is unavailable
type QuoteItemRecord = {
  id?: string
  quote_id?: string
  category: string
  description: string
  quantity: number
  unit: 'm2' | 'h' | 'u' | string
  unit_price: number
  material_cost: number
  labor_cost: number
  complexity_factor: number
  order_index: number
  is_optional: boolean
  ai_suggested: boolean
  total_price: number
}

type QuoteRecord = {
  id?: string
  request_id: string
  version: number
  status: 'brouillon' | string
  total_ht: number
  total_ttc: number
  tva_rate: number
  margin_rate: number
  created_by: string
  modified_by: string
  ai_generated_data?: any
  validity_days: number
  internal_comments?: string
  custom_conditions?: string
}

type QuoteWithItems = QuoteRecord & { items: QuoteItemRecord[] }
type LineItem = {
  id?: string
  label: string
  category: string
  quantity: number
  unit: string
  unitPrice: number
  total: number
  editable: boolean
}
type Quote = {
  id?: string
  currency: string
  items: LineItem[]
  totals: {
    subtotal: number
    discount: number
    margin: number
    vatRate: number
    vatAmount: number
    total: number
  }
  notes?: string
  aiEstimateTotal?: number
  aiConfidence?: number
}

function withTotals(items: Omit<QuoteItemRecord, 'total_price'>[]): QuoteItemRecord[] {
  return items.map(it => ({
    ...it,
    total_price: round2(it.quantity * it.unit_price * it.complexity_factor),
  }))
}

function round2(n: number) {
  return Math.round(n * 100) / 100
}

describe('calculateQuoteTotals', () => {
  it('computes totals with default margin (15%) and TVA (20%)', () => {
    const items = withTotals([
      { category: 'paint', description: 'A', quantity: 10, unit: 'm2', unit_price: 12, material_cost: 5, labor_cost: 7, complexity_factor: 1, order_index: 0, is_optional: false, ai_suggested: false },
      { category: 'labor', description: 'B', quantity: 5, unit: 'h', unit_price: 30, material_cost: 0, labor_cost: 30, complexity_factor: 1, order_index: 1, is_optional: false, ai_suggested: false },
    ])
    const res = calculateQuoteTotals(items, DEFAULT_MARGIN * 100, undefined as any)
    // subtotal = 10*12 + 5*30 = 120 + 150 = 270
    expect(res.subtotal).toBe(270)
    // margin = 15% of 270 = 40.5
    expect(res.margin).toBe(40.5)
    // total_ht = 310.5
    expect(res.total_ht).toBe(310.5)
    // tva_amount = 20% of 310.5 = 62.1
    expect(res.tva_amount).toBe(62.1)
    // total_ttc = 372.6
    expect(res.total_ttc).toBe(372.6)
  })

  it('respects provided custom rates and rounds to 2 decimals', () => {
    const items = withTotals([
      { category: 'x', description: 'x', quantity: 3, unit: 'u', unit_price: 33.333, material_cost: 0, labor_cost: 0, complexity_factor: 1.111, order_index: 0, is_optional: false, ai_suggested: false },
    ])
    const res = calculateQuoteTotals(items, 12.5, 19.6)
    expect(res.subtotal).toBe(round2(3 * 33.333 * 1.111)) // verify rounding behavior
    expect(res.margin).toBe(round2(res.subtotal * 0.125))
    expect(res.total_ht).toBe(round2(res.subtotal + res.margin))
    expect(res.tva_amount).toBe(round2(res.total_ht * 0.196))
    expect(res.total_ttc).toBe(round2(res.total_ht + res.tva_amount))
  })

  it('handles empty items array', () => {
    const res = calculateQuoteTotals([], 10, 5)
    expect(res).toEqual({
      subtotal: 0,
      margin: 0,
      total_ht: 0,
      tva_amount: 0,
      total_ttc: 0,
    })
  })
})

describe('estimateProjectDuration', () => {
  it('sums h units and adds 2 hours per m2, minimum 4h', () => {
    const items = withTotals([
      { category: 'labor', description: 'prep', quantity: 3, unit: 'h', unit_price: 50, material_cost: 0, labor_cost: 50, complexity_factor: 1, order_index: 0, is_optional: false, ai_suggested: true },
      { category: 'paint', description: 'walls', quantity: 10, unit: 'm2', unit_price: 15, material_cost: 8, labor_cost: 7, complexity_factor: 1, order_index: 1, is_optional: false, ai_suggested: true },
    ])
    // 3h + 10m2*2 = 23 => round(23) = 23
    expect(estimateProjectDuration(items)).toBe(23)
  })

  it('enforces minimum of 4 hours', () => {
    const items = withTotals([
      { category: 'labor', description: 'tiny', quantity: 0.5, unit: 'h', unit_price: 50, material_cost: 0, labor_cost: 50, complexity_factor: 1, order_index: 0, is_optional: false, ai_suggested: true },
    ])
    expect(estimateProjectDuration(items)).toBe(4)
  })
})

describe('convertLegacyQuoteToDatabase', () => {
  function makeLegacy(): Quote {
    return {
      currency: 'EUR',
      items: [
        { id: 'li1', label: 'Item 1', category: 'peinture', quantity: 2, unit: 'm2', unitPrice: 10, total: 20, editable: true },
        { id: 'li2', label: 'Item 2', category: 'électricité', quantity: 3, unit: 'h', unitPrice: 40, total: 120, editable: true },
      ],
      totals: {
        subtotal: 140,
        discount: 0,
        margin: 14,
        vatRate: 0.2,
        vatAmount: 30.8,
        total: 184.8,
      },
      notes: 'note',
      aiEstimateTotal: 180,
      aiConfidence: 0.8,
    }
  }

  it('maps fields and computes percentage rates', () => {
    const legacy = makeLegacy()
    const { quote, items } = convertLegacyQuoteToDatabase(legacy as any, 'REQ-1', 'USER-1')

    expect(quote.request_id).toBe('REQ-1')
    expect(quote.version).toBe(1)
    expect(quote.status).toBe('brouillon')
    expect(quote.total_ht).toBe(legacy.totals.subtotal)
    expect(quote.total_ttc).toBe(legacy.totals.total)
    expect(quote.tva_rate).toBe(20) // 0.2 -> 20
    expect(round2(quote.margin_rate)).toBe(round2((legacy.totals.margin / legacy.totals.subtotal) * 100))
    expect(quote.created_by).toBe('USER-1')
    expect(quote.modified_by).toBe('USER-1')
    expect(quote.validity_days).toBe(30)
    expect(quote.ai_generated_data?.original_quote).toBe(legacy)
    expect(quote.ai_generated_data?.aiEstimateTotal).toBe(legacy.aiEstimateTotal)
    expect(quote.ai_generated_data?.aiConfidence).toBe(legacy.aiConfidence)

    expect(items).toHaveLength(2)
    expect(items[0]).toMatchObject({
      category: 'peinture',
      description: 'Item 1',
      quantity: 2,
      unit: 'm2',
      unit_price: 10,
      material_cost: 0,
      labor_cost: 0,
      complexity_factor: 1,
      order_index: 0,
      is_optional: false,
      ai_suggested: true,
    })
  })
})

describe('convertDatabaseQuoteToLegacy', () => {
  it('converts fields including vat rate back to decimal and preserves AI hints', () => {
    const items = withTotals([
        { category: 'peinture', description: 'Walls', quantity: 4, unit: 'm2', unit_price: 10, material_cost: 5, labor_cost: 5, complexity_factor: 1, order_index: 0, is_optional: false, ai_suggested: true, id: 'I1' },
      ]);
    const subtotal = items.reduce((s, it) => s + it.total_price, 0); // 40
    const margin_rate = 10;
    const tva_rate = 20;
    const margin = subtotal * (margin_rate / 100); // 4
    const total_ht = subtotal + margin; // 44
    const total_ttc = total_ht * (1 + tva_rate / 100); // 52.8

    const quote: QuoteWithItems = {
      id: 'Q1',
      request_id: 'REQ',
      version: 1,
      status: 'brouillon',
      total_ht,
      total_ttc,
      tva_rate,
      margin_rate,
      created_by: 'U',
      modified_by: 'U',
      validity_days: 30,
      internal_comments: 'internal',
      ai_generated_data: { aiEstimateTotal: 118, aiConfidence: 0.9 },
      items,
    }

    const res = convertDatabaseQuoteToLegacy(quote as any)

    expect(res.id).toBe('Q1')
    expect(res.currency).toBe('EUR')
    expect(res.items[0]).toMatchObject({
      id: 'I1',
      label: 'Walls',
      category: 'peinture',
      quantity: 4,
      unit: 'm2',
      unitPrice: 10,
      total: round2(4 * 10 * 1),
      editable: true,
    })
    expect(res.totals.subtotal).toBeCloseTo(subtotal)
    expect(res.totals.margin).toBeCloseTo(margin)
    expect(res.totals.vatRate).toBeCloseTo(tva_rate / 100)
    expect(res.totals.vatAmount).toBeCloseTo(total_ttc - total_ht)
    expect(res.totals.total).toBeCloseTo(total_ttc)
    expect(res.notes).toBe('internal')
    expect(res.aiEstimateTotal).toBe(118)
    expect(res.aiConfidence).toBe(0.9)
  })

  it('falls back to totals when AI data absent', () => {
    const q: QuoteWithItems = {
      id: 'Q2',
      request_id: 'REQ',
      version: 1,
      status: 'brouillon',
      total_ht: 80,
      total_ttc: 96,
      tva_rate: 20,
      margin_rate: 0,
      created_by: 'U',
      modified_by: 'U',
      validity_days: 30,
      items: [],
    } as any
    const res = convertDatabaseQuoteToLegacy(q as any)
    expect(res.aiEstimateTotal).toBe(96)
    expect(res.aiConfidence).toBeCloseTo(0.7)
  })

  it('correctly calculates totals from the quote record, not from items', () => {
    const quote: QuoteWithItems = {
      id: 'Q3',
      request_id: 'REQ',
      version: 1,
      status: 'brouillon',
      total_ht: 110, // authoritative total_ht
      total_ttc: 132, // authoritative total_ttc
      tva_rate: 20,
      margin_rate: 10,
      created_by: 'U',
      modified_by: 'U',
      validity_days: 30,
      items: withTotals([ // items sum to 100, but total_ht is 110
        { category: 'x', description: 'Item', quantity: 1, unit_price: 100, unit: 'u', material_cost: 0, labor_cost: 0, complexity_factor: 1, order_index: 0, is_optional: false, ai_suggested: false },
      ]),
    }

    const res = convertDatabaseQuoteToLegacy(quote as any)

    // The function should derive totals from the quote's totals, not by summing items.
    expect(res.totals.subtotal).toBeCloseTo(100) // 110 / 1.1
    expect(res.totals.margin).toBeCloseTo(10)     // 110 - 100
    expect(res.totals.vatAmount).toBeCloseTo(22)  // 132 - 110
    expect(res.totals.total).toBe(132)
  })
})

describe('generateBaseQuoteItems', () => {
  it('generates expected items for électricité with high urgency (adds surcharge item)', () => {
    const items = generateBaseQuoteItems({
      typeRenovation: 'électricité',
      surfaceM2: 20,
      urgence: 'haute',
    } as any)

    // Should include 2 base items + 1 urgency surcharge
    expect(items.length).toBe(3)
    const surcharge = items[2]
    expect(surcharge.category).toBe('autre')
    expect(surcharge.is_optional).toBe(true)
    // Unit price approx 15% of sum(quantity*unit_price) of previous items
    const baseSum = items.slice(0, 2).reduce((s, it) => s + it.quantity * it.unit_price, 0)
    expect(surcharge.unit_price).toBe(Math.round(baseSum * DEFAULT_MARGIN))
  })

  it('handles unknown renovation type via default case', () => {
    const items = generateBaseQuoteItems({
      typeRenovation: 'isolation',
      surfaceM2: 10,
      urgence: 'basse',
    } as any)
    expect(items.length).toBe(1)
    expect(items[0]).toMatchObject({
      category: 'isolation',
      description: 'Rénovation isolation',
      unit: 'm2',
      unit_price: 50,
      material_cost: 30,
      labor_cost: 20,
      ai_suggested: true,
    })
  })
})

describe('validateQuote', () => {
  function makeQuote(items: QuoteItemRecord[], overrides: Partial<QuoteWithItems> = {}): QuoteWithItems {
    const withTotalsComputed = items.map(i => ({
      ...i,
      total_price: round2(i.quantity * i.unit_price * i.complexity_factor),
    }))
    const totals = calculateQuoteTotals(withTotalsComputed, 10, 20)
    return {
      id: 'Q',
      request_id: 'REQ',
      version: 1,
      status: 'brouillon',
      total_ht: totals.total_ht,
      total_ttc: totals.total_ttc,
      tva_rate: 20,
      margin_rate: 10,
      created_by: 'U',
      modified_by: 'U',
      validity_days: 30,
      items: withTotalsComputed,
      ...overrides,
    }
  }

  it('flags missing items and invalid totals', () => {
    const q: QuoteWithItems = {
      id: 'Q',
      request_id: 'REQ',
      version: 1,
      status: 'brouillon',
      total_ht: 0,
      total_ttc: 0,
      tva_rate: 20,
      margin_rate: 10,
      created_by: 'U',
      modified_by: 'U',
      validity_days: 30,
      items: [],
    }
    const res = validateQuote(q)
    expect(res.isValid).toBe(false)
    expect(res.errors).toEqual(expect.arrayContaining([
      'Le devis doit contenir au moins un item',
      'Le montant HT doit être positif',
      'Le montant TTC doit être supérieur au montant HT',
    ]))
  })

  it('emits item-level warnings and errors for edge values', () => {
    const items = withTotals([
      { id: 'i1', category: 'x', description: 'ok', quantity: 1, unit: 'u', unit_price: 0, material_cost: 0, labor_cost: 0, complexity_factor: 1, order_index: 0, is_optional: false, ai_suggested: false },
      { id: 'i2', category: 'x', description: ' ', quantity: -1, unit: 'u', unit_price: -5, material_cost: 0, labor_cost: 0, complexity_factor: 1, order_index: 1, is_optional: false, ai_suggested: false },
      // Make an incoherent total by tweaking complexity factor after total calc
      { id: 'i3', category: 'x', description: 'bad total', quantity: 2, unit: 'u', unit_price: 10, material_cost: 0, labor_cost: 0, complexity_factor: 2, order_index: 2, is_optional: false, ai_suggested: false },
    ])
    // Corrupt total of i3 to cause mismatch
    items[2].total_price = 1

    const q = makeQuote(items, { validity_days: 10, margin_rate: 50 })
    const res = validateQuote(q)

    expect(res.isValid).toBe(false)

    expect(res.errors).toEqual(expect.arrayContaining([
      "L'item 2 doit avoir une description",
      "L'item 2 doit avoir une quantité positive",
      "L'item 2 ne peut pas avoir un prix unitaire négatif",
    ]))

    expect(res.warnings).toEqual(expect.arrayContaining([
      "L'item 1 a un prix unitaire de 0€",
      "L'item 3 a un total incohérent avec le calcul automatique",
      'Les totaux calculés ne correspondent pas aux totaux enregistrés',
      'La validité du devis est très courte (moins de 15 jours)',
      'La marge est très élevée (plus de 40%)',
      "Le montant TTC doit être supérieur au montant HT"
    ]))
  })

  it('warns for low margin', () => {
    const items = withTotals([
      { id: 'i1', category: 'x', description: 'ok', quantity: 1, unit: 'u', unit_price: 100, material_cost: 0, labor_cost: 0, complexity_factor: 1, order_index: 0, is_optional: false, ai_suggested: false },
    ])
    const q = {
      id: 'Q',
      request_id: 'REQ',
      version: 1,
      status: 'brouillon',
      total_ht: 100,
      total_ttc: 120,
      tva_rate: 20,
      margin_rate: 4.5,
      created_by: 'U',
      modified_by: 'U',
      validity_days: 30,
      items,
    } as QuoteWithItems
    const res = validateQuote(q)
    expect(res.isValid).toBe(true)
    expect(res.warnings).toEqual(expect.arrayContaining([
      'La marge est très faible (moins de 5%)',
    ]))
  })
})

describe('compareQuoteVersions', () => {
  function baseItem(overrides: Partial<QuoteItemRecord> = {}): QuoteItemRecord {
    return {
      id: 'I1',
      category: 'x',
      description: 'desc',
      quantity: 1,
      unit: 'u',
      unit_price: 10,
      material_cost: 0,
      labor_cost: 0,
      complexity_factor: 1,
      order_index: 0,
      is_optional: false,
      ai_suggested: false,
      total_price: 10,
      ...overrides,
    }
  }
  function baseQuote(overrides: Partial<QuoteWithItems> = {}): QuoteWithItems {
    return {
      id: 'Q',
      request_id: 'REQ',
      version: 1,
      status: 'brouillon',
      total_ht: 100,
      total_ttc: 120,
      tva_rate: 20,
      margin_rate: 10,
      created_by: 'U',
      modified_by: 'U',
      validity_days: 30,
      items: [baseItem()],
      ...overrides,
    }
  }

  it('detects quote-level field changes', () => {
    const oldQ = baseQuote()
    const newQ = baseQuote({ total_ttc: 121, validity_days: 45, custom_conditions: 'new' })
    const diff = compareQuoteVersions(oldQ, newQ)

    expect(diff.quote.total_ttc).toEqual({ old: 120, new: 121 })
    expect(diff.quote.validity_days).toEqual({ old: 30, new: 45 })
    expect(diff.quote.custom_conditions).toEqual({ old: undefined, new: 'new' })
  })

  it('reports added, removed, and modified items', () => {
    const oldQ = baseQuote({
      items: [baseItem({ id: 'A', description: 'Old', unit_price: 10 })],
    })
    const newQ = baseQuote({
      items: [
        baseItem({ id: 'A', description: 'New', unit_price: 12 }), // modified
        baseItem({ id: 'B', description: 'Added', unit_price: 5 }), // added
      ],
    })

    const diff = compareQuoteVersions(oldQ, newQ)

    expect(diff.items.added.map(i => i.id)).toEqual(['B'])
    expect(diff.items.removed.map(i => i.id)).toEqual([]) // none removed
    expect(diff.items.modified).toHaveLength(1)
    expect(diff.items.modified[0].old.description).toBe('Old')
    expect(diff.items.modified[0].new.description).toBe('New')
  })

  it('detects removed items', () => {
    const oldQ = baseQuote({ items: [baseItem({ id: 'A' }), baseItem({ id: 'B' })] })
    const newQ = baseQuote({ items: [baseItem({ id: 'A' })] })
    const diff = compareQuoteVersions(oldQ, newQ)
    expect(diff.items.removed.map(i => i.id)).toEqual(['B'])
  })
})
