import { DEFAULT_VAT, productCatalog, laborRates, complexityFactor, categoryKeywords } from "./catalog"
import { DEFAULT_MARGIN } from "./quote-constants"
import type { Category, ClientInput, LineItem, Quote } from "./types"

function generateId(): string {
  return `${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

function detectCategory(text: string, fallback: Category): Category {
  const lower = text.toLowerCase()
  let best: { cat: Category; score: number } = { cat: fallback, score: 0 }
  for (const [cat, keys] of Object.entries(categoryKeywords) as [Category, string[]][]) {
    const s = keys.reduce((acc, k) => (lower.includes(k) ? acc + 1 : acc), 0)
    if (s > best.score) best = { cat, score: s }
  }
  return best.score > 0 ? best.cat : fallback
}

export function buildQuote(input: ClientInput): Quote {
  const fileCount = input.files?.length ?? 0
  const cat = detectCategory(input.description, input.typeRenovation)
  const area = Math.max(0, input.surfaceM2 ?? 0)
  const factor = complexityFactor(input.urgence, input.description.length, fileCount)

  // seed items from catalog
  const base = productCatalog[cat]?.[0]
  const items: LineItem[] = []
  if (area > 0 && base) {
    const quantity = base.unit === "m2" ? area : Math.max(1, Math.round(area / 5))
    items.push({
      id: generateId(),
      label: base.label,
      category: cat,
      quantity,
      unit: base.unit,
      unitPrice: base.unitPrice,
      total: Number((quantity * base.unitPrice).toFixed(2)),
      editable: true,
    })
  } else if (base) {
    items.push({
      id: generateId(),
      label: base.label,
      category: cat,
      quantity: 1,
      unit: base.unit,
      unitPrice: base.unitPrice,
      total: Number((1 * base.unitPrice).toFixed(2)),
      editable: true,
    })
  }

  // add labor
  const hours = area > 0 ? Math.max(2, Math.round((area / 10) * factor)) : Math.max(2, Math.round(4 * factor))
  const moRate = laborRates[cat] ?? 50
  items.push({
    id: generateId(),
    label: "Main d'œuvre",
    category: cat,
    quantity: hours,
    unit: "h",
    unitPrice: moRate,
    total: Number((hours * moRate).toFixed(2)),
    editable: true,
  })

  // complexity contingency line
  if (factor > 1.05) {
    const contingency = Math.round((items.reduce((a, b) => a + b.total, 0) * (factor - 1)) / 10) * 10
    items.push({
      id: generateId(),
      label: "Aléas et complexité",
      category: cat,
      quantity: 1,
      unit: "u",
      unitPrice: contingency,
      total: contingency,
      editable: true,
    })
  }

  const subtotal = items.reduce((a, b) => a + b.total, 0)
  const margin = Math.round(subtotal * DEFAULT_MARGIN * 100) / 100
  const vatRate = DEFAULT_VAT
  const vatAmount = Math.round((subtotal + margin) * vatRate * 100) / 100
  const total = Math.round((subtotal + margin + vatAmount) * 100) / 100

  const summary =
    `Analyse IA (règles) – Catégorie probable: ${cat}. ` +
    `Surface: ${area || "n/a"} m². Urgence: ${input.urgence}. ` +
    `Complexité relative: x${factor}. Estimation initiale: ~${total.toLocaleString("fr-FR")} € TTC.`

  const confidence = 0.65 + Math.min(0.25, (area ? 0.1 : 0) + (fileCount > 2 ? 0.1 : 0) + (factor > 1 ? 0.05 : 0))

  return {
    id: `q_${generateId()}`,
    currency: "EUR",
    items,
    totals: {
      subtotal: Math.round(subtotal * 100) / 100,
      discount: 0,
      margin,
      vatRate,
      vatAmount,
      total,
    },
    notes: summary,
    aiEstimateTotal: total,
    aiConfidence: Number(confidence.toFixed(2)),
  }
}

export function recalcTotals(items: LineItem[], vatRate: number, margin: number, discount: number) {
  const subtotal = Math.max(0, Math.round(items.reduce((a, b) => a + b.quantity * b.unitPrice, 0) * 100) / 100)
  const vatAmount = Math.round(Math.max(0, subtotal + margin - discount) * vatRate * 100) / 100
  const total = Math.round((subtotal + margin - discount + vatAmount) * 100) / 100
  return { subtotal, vatAmount, total }
}
