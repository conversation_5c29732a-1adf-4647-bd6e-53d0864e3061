"use client"
import { createBrowserClient } from "@supabase/ssr"
import type { Database } from "./types"

let client: ReturnType<typeof createBrowserClient<Database>> | null = null

export const supabase = (() => {
  if (client) return client
  const url = process.env.NEXT_PUBLIC_SUPABASE_URL
  const anon = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  if (!url || !anon) {
    throw new Error("Missing Supabase env (NEXT_PUBLIC_SUPABASE_URL / NEXT_PUBLIC_SUPABASE_ANON_KEY)")
  }
  client = createBrowserClient<Database>(url, anon)
  return client
})()
