export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export type Database = {
  // Allows to automatically instantiate createClient with right options
  // instead of createClient<Database, { PostgrestVersion: 'XX' }>(URL, KEY)
  __InternalSupabase: {
    PostgrestVersion: "13.0.4"
  }
  public: {
    Tables: {
      files: {
        Row: {
          created_at: string
          id: string
          name: string
          request_id: string | null
          size: number | null
          type: string
          url: string | null
        }
        Insert: {
          created_at?: string
          id?: string
          name: string
          request_id?: string | null
          size?: number | null
          type: string
          url?: string | null
        }
        Update: {
          created_at?: string
          id?: string
          name?: string
          request_id?: string | null
          size?: number | null
          type?: string
          url?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "files_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      invoices: {
        Row: {
          accounting_export_date: string | null
          accounting_exported: boolean | null
          client_address: string | null
          client_email: string
          client_name: string
          client_siret: string | null
          created_at: string | null
          created_by: string
          due_date: string | null
          fiscal_year: number | null
          id: string
          invoice_number: string
          items: Json
          notes: string | null
          paid_at: string | null
          payment_date: string | null
          payment_method: string | null
          payment_reference: string | null
          payment_terms: number | null
          quote_id: string
          request_id: string
          sent_at: string | null
          status: string | null
          total_ht: number
          total_ttc: number
          tva_amount: number
          tva_rate: number
          updated_at: string | null
        }
        Insert: {
          accounting_export_date?: string | null
          accounting_exported?: boolean | null
          client_address?: string | null
          client_email: string
          client_name: string
          client_siret?: string | null
          created_at?: string | null
          created_by: string
          due_date?: string | null
          fiscal_year?: number | null
          id?: string
          invoice_number: string
          items?: Json
          notes?: string | null
          paid_at?: string | null
          payment_date?: string | null
          payment_method?: string | null
          payment_reference?: string | null
          payment_terms?: number | null
          quote_id: string
          request_id: string
          sent_at?: string | null
          status?: string | null
          total_ht: number
          total_ttc: number
          tva_amount: number
          tva_rate: number
          updated_at?: string | null
        }
        Update: {
          accounting_export_date?: string | null
          accounting_exported?: boolean | null
          client_address?: string | null
          client_email?: string
          client_name?: string
          client_siret?: string | null
          created_at?: string | null
          created_by?: string
          due_date?: string | null
          fiscal_year?: number | null
          id?: string
          invoice_number?: string
          items?: Json
          notes?: string | null
          paid_at?: string | null
          payment_date?: string | null
          payment_method?: string | null
          payment_reference?: string | null
          payment_terms?: number | null
          quote_id?: string
          request_id?: string
          sent_at?: string | null
          status?: string | null
          total_ht?: number
          total_ttc?: number
          tva_amount?: number
          tva_rate?: number
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "invoices_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes_with_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "invoices_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      notifications: {
        Row: {
          clicked_at: string | null
          created_at: string | null
          delivered_at: string | null
          failed_reason: string | null
          id: string
          max_retries: number | null
          message: string | null
          metadata: Json | null
          quote_id: string | null
          read_at: string | null
          recipient_email: string
          recipient_name: string | null
          request_id: string | null
          retry_count: number | null
          scheduled_at: string | null
          sent_at: string | null
          status: string | null
          subject: string | null
          type: string
          updated_at: string | null
        }
        Insert: {
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          failed_reason?: string | null
          id?: string
          max_retries?: number | null
          message?: string | null
          metadata?: Json | null
          quote_id?: string | null
          read_at?: string | null
          recipient_email: string
          recipient_name?: string | null
          request_id?: string | null
          retry_count?: number | null
          scheduled_at?: string | null
          sent_at?: string | null
          status?: string | null
          subject?: string | null
          type: string
          updated_at?: string | null
        }
        Update: {
          clicked_at?: string | null
          created_at?: string | null
          delivered_at?: string | null
          failed_reason?: string | null
          id?: string
          max_retries?: number | null
          message?: string | null
          metadata?: Json | null
          quote_id?: string | null
          read_at?: string | null
          recipient_email?: string
          recipient_name?: string | null
          request_id?: string | null
          retry_count?: number | null
          scheduled_at?: string | null
          sent_at?: string | null
          status?: string | null
          subject?: string | null
          type?: string
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "notifications_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes_with_items"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "notifications_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      products_catalog: {
        Row: {
          availability: string | null
          brand: string | null
          category: string
          cost_price: number | null
          created_at: string | null
          current_price: number
          description: string | null
          id: string
          is_active: boolean | null
          last_price_update: string | null
          lead_time_days: number | null
          minimum_quantity: number | null
          name: string
          reference: string | null
          region: string | null
          specifications: Json | null
          subcategory: string | null
          supplier: string | null
          supplier_url: string | null
          tags: string[] | null
          unit: string
          updated_at: string | null
        }
        Insert: {
          availability?: string | null
          brand?: string | null
          category: string
          cost_price?: number | null
          created_at?: string | null
          current_price: number
          description?: string | null
          id?: string
          is_active?: boolean | null
          last_price_update?: string | null
          lead_time_days?: number | null
          minimum_quantity?: number | null
          name: string
          reference?: string | null
          region?: string | null
          specifications?: Json | null
          subcategory?: string | null
          supplier?: string | null
          supplier_url?: string | null
          tags?: string[] | null
          unit?: string
          updated_at?: string | null
        }
        Update: {
          availability?: string | null
          brand?: string | null
          category?: string
          cost_price?: number | null
          created_at?: string | null
          current_price?: number
          description?: string | null
          id?: string
          is_active?: boolean | null
          last_price_update?: string | null
          lead_time_days?: number | null
          minimum_quantity?: number | null
          name?: string
          reference?: string | null
          region?: string | null
          specifications?: Json | null
          subcategory?: string | null
          supplier?: string | null
          supplier_url?: string | null
          tags?: string[] | null
          unit?: string
          updated_at?: string | null
        }
        Relationships: []
      }
      profiles: {
        Row: {
          address: string | null
          city: string | null
          company_name: string | null
          created_at: string
          email: string | null
          id: string
          name: string | null
          postal_code: string | null
          role: string | null
          siret: string | null
          updated_at: string
        }
        Insert: {
          address?: string | null
          city?: string | null
          company_name?: string | null
          created_at?: string
          email?: string | null
          id: string
          name?: string | null
          postal_code?: string | null
          role?: string | null
          siret?: string | null
          updated_at?: string
        }
        Update: {
          address?: string | null
          city?: string | null
          company_name?: string | null
          created_at?: string
          email?: string | null
          id?: string
          name?: string | null
          postal_code?: string | null
          role?: string | null
          siret?: string | null
          updated_at?: string
        }
        Relationships: []
      }
      quote_history: {
        Row: {
          action: string
          changes: Json | null
          comment: string | null
          created_at: string | null
          id: string
          ip_address: unknown | null
          modified_by: string
          new_values: Json | null
          previous_values: Json | null
          quote_id: string
          user_agent: string | null
          version: number
        }
        Insert: {
          action: string
          changes?: Json | null
          comment?: string | null
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          modified_by: string
          new_values?: Json | null
          previous_values?: Json | null
          quote_id: string
          user_agent?: string | null
          version: number
        }
        Update: {
          action?: string
          changes?: Json | null
          comment?: string | null
          created_at?: string | null
          id?: string
          ip_address?: unknown | null
          modified_by?: string
          new_values?: Json | null
          previous_values?: Json | null
          quote_id?: string
          user_agent?: string | null
          version?: number
        }
        Relationships: [
          {
            foreignKeyName: "quote_history_modified_by_fkey"
            columns: ["modified_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quote_history_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quote_history_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes_with_items"
            referencedColumns: ["id"]
          },
        ]
      }
      quote_items: {
        Row: {
          ai_suggested: boolean | null
          category: string
          complexity_factor: number | null
          created_at: string | null
          description: string
          id: string
          is_optional: boolean | null
          labor_cost: number | null
          material_cost: number | null
          notes: string | null
          order_index: number | null
          quantity: number | null
          quote_id: string
          subcategory: string | null
          supplier: string | null
          total_price: number | null
          unit: string | null
          unit_price: number | null
          updated_at: string | null
        }
        Insert: {
          ai_suggested?: boolean | null
          category: string
          complexity_factor?: number | null
          created_at?: string | null
          description: string
          id?: string
          is_optional?: boolean | null
          labor_cost?: number | null
          material_cost?: number | null
          notes?: string | null
          order_index?: number | null
          quantity?: number | null
          quote_id: string
          subcategory?: string | null
          supplier?: string | null
          total_price?: number | null
          unit?: string | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Update: {
          ai_suggested?: boolean | null
          category?: string
          complexity_factor?: number | null
          created_at?: string | null
          description?: string
          id?: string
          is_optional?: boolean | null
          labor_cost?: number | null
          material_cost?: number | null
          notes?: string | null
          order_index?: number | null
          quantity?: number | null
          quote_id?: string
          subcategory?: string | null
          supplier?: string | null
          total_price?: number | null
          unit?: string | null
          unit_price?: number | null
          updated_at?: string | null
        }
        Relationships: [
          {
            foreignKeyName: "quote_items_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quote_items_quote_id_fkey"
            columns: ["quote_id"]
            isOneToOne: false
            referencedRelation: "quotes_with_items"
            referencedColumns: ["id"]
          },
        ]
      }
      quotes: {
        Row: {
          ai_generated_data: Json | null
          created_at: string | null
          created_by: string | null
          custom_conditions: string | null
          id: string
          internal_comments: string | null
          margin_rate: number | null
          modified_by: string | null
          request_id: string
          status: string | null
          template_id: string | null
          total_ht: number | null
          total_ttc: number | null
          tva_rate: number | null
          updated_at: string | null
          validity_days: number | null
          version: number | null
        }
        Insert: {
          ai_generated_data?: Json | null
          created_at?: string | null
          created_by?: string | null
          custom_conditions?: string | null
          id?: string
          internal_comments?: string | null
          margin_rate?: number | null
          modified_by?: string | null
          request_id: string
          status?: string | null
          template_id?: string | null
          total_ht?: number | null
          total_ttc?: number | null
          tva_rate?: number | null
          updated_at?: string | null
          validity_days?: number | null
          version?: number | null
        }
        Update: {
          ai_generated_data?: Json | null
          created_at?: string | null
          created_by?: string | null
          custom_conditions?: string | null
          id?: string
          internal_comments?: string | null
          margin_rate?: number | null
          modified_by?: string | null
          request_id?: string
          status?: string | null
          template_id?: string | null
          total_ht?: number | null
          total_ttc?: number | null
          tva_rate?: number | null
          updated_at?: string | null
          validity_days?: number | null
          version?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_quotes_template_id"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotes_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotes_modified_by_fkey"
            columns: ["modified_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotes_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
      requests: {
        Row: {
          ai_summary: string | null
          assigned_to: string | null
          client_email: string | null
          client_id: string | null
          client_name: string | null
          created_at: string
          id: string
          input: Json
          invoice_no: string | null
          previews: Json | null
          priority: number
          quote: Json | null
          search_vector: unknown | null
          status: string
          updated_at: string
        }
        Insert: {
          ai_summary?: string | null
          assigned_to?: string | null
          client_email?: string | null
          client_id?: string | null
          client_name?: string | null
          created_at?: string
          id: string
          input: Json
          invoice_no?: string | null
          previews?: Json | null
          priority?: number
          quote?: Json | null
          search_vector?: unknown | null
          status?: string
          updated_at?: string
        }
        Update: {
          ai_summary?: string | null
          assigned_to?: string | null
          client_email?: string | null
          client_id?: string | null
          client_name?: string | null
          created_at?: string
          id?: string
          input?: Json
          invoice_no?: string | null
          previews?: Json | null
          priority?: number
          quote?: Json | null
          search_vector?: unknown | null
          status?: string
          updated_at?: string
        }
        Relationships: [
          {
            foreignKeyName: "requests_assigned_to_fkey"
            columns: ["assigned_to"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "requests_client_id_fkey"
            columns: ["client_id"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
      templates: {
        Row: {
          category: string
          complexity_level: number | null
          created_at: string | null
          created_by: string
          default_margin: number | null
          description: string | null
          estimated_duration_hours: number | null
          id: string
          is_public: boolean | null
          items: Json
          name: string
          notes: string | null
          subcategory: string | null
          tags: string[] | null
          updated_at: string | null
          usage_count: number | null
        }
        Insert: {
          category: string
          complexity_level?: number | null
          created_at?: string | null
          created_by: string
          default_margin?: number | null
          description?: string | null
          estimated_duration_hours?: number | null
          id?: string
          is_public?: boolean | null
          items?: Json
          name: string
          notes?: string | null
          subcategory?: string | null
          tags?: string[] | null
          updated_at?: string | null
          usage_count?: number | null
        }
        Update: {
          category?: string
          complexity_level?: number | null
          created_at?: string | null
          created_by?: string
          default_margin?: number | null
          description?: string | null
          estimated_duration_hours?: number | null
          id?: string
          is_public?: boolean | null
          items?: Json
          name?: string
          notes?: string | null
          subcategory?: string | null
          tags?: string[] | null
          updated_at?: string | null
          usage_count?: number | null
        }
        Relationships: [
          {
            foreignKeyName: "templates_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Views: {
      dashboard_stats: {
        Row: {
          accepted_quotes: number | null
          analyzing_requests: number | null
          avg_quote_amount: number | null
          draft_quotes: number | null
          pending_requests: number | null
          sent_quotes: number | null
          total_accepted_amount: number | null
        }
        Relationships: []
      }
      quotes_with_items: {
        Row: {
          ai_generated_data: Json | null
          ai_summary: string | null
          client_email: string | null
          client_name: string | null
          created_at: string | null
          created_by: string | null
          custom_conditions: string | null
          id: string | null
          internal_comments: string | null
          items: Json | null
          margin_rate: number | null
          modified_by: string | null
          request_id: string | null
          request_status: string | null
          status: string | null
          template_id: string | null
          total_ht: number | null
          total_ttc: number | null
          tva_rate: number | null
          updated_at: string | null
          validity_days: number | null
          version: number | null
        }
        Relationships: [
          {
            foreignKeyName: "fk_quotes_template_id"
            columns: ["template_id"]
            isOneToOne: false
            referencedRelation: "templates"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotes_created_by_fkey"
            columns: ["created_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotes_modified_by_fkey"
            columns: ["modified_by"]
            isOneToOne: false
            referencedRelation: "profiles"
            referencedColumns: ["id"]
          },
          {
            foreignKeyName: "quotes_request_id_fkey"
            columns: ["request_id"]
            isOneToOne: false
            referencedRelation: "requests"
            referencedColumns: ["id"]
          },
        ]
      }
    }
    Functions: {
      convert_quote_to_invoice: {
        Args: { quote_uuid: string }
        Returns: string
      }
      generate_invoice_number: {
        Args: Record<PropertyKey, never>
        Returns: string
      }
      get_templates_by_category: {
        Args: { p_category: string }
        Returns: {
          category: string
          complexity_level: number | null
          created_at: string | null
          created_by: string
          default_margin: number | null
          description: string | null
          estimated_duration_hours: number | null
          id: string
          is_public: boolean | null
          items: Json
          name: string
          notes: string | null
          subcategory: string | null
          tags: string[] | null
          updated_at: string | null
          usage_count: number | null
        }[]
      }
      get_user_templates: {
        Args: Record<PropertyKey, never>
        Returns: {
          category: string
          complexity_level: number | null
          created_at: string | null
          created_by: string
          default_margin: number | null
          description: string | null
          estimated_duration_hours: number | null
          id: string
          is_public: boolean | null
          items: Json
          name: string
          notes: string | null
          subcategory: string | null
          tags: string[] | null
          updated_at: string | null
          usage_count: number | null
        }[]
      }
      increment_template_usage: {
        Args: { template_uuid: string }
        Returns: undefined
      }
      schedule_quote_reminder: {
        Args: {
          days_delay?: number
          quote_uuid: string
          reminder_type?: string
        }
        Returns: string
      }
      update_product_price: {
        Args: { new_price: number; product_uuid: string }
        Returns: undefined
      }
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}

type DatabaseWithoutInternals = Omit<Database, "__InternalSupabase">

type DefaultSchema = DatabaseWithoutInternals[Extract<keyof Database, "public">]

export type Tables<
  DefaultSchemaTableNameOrOptions extends
    | keyof (DefaultSchema["Tables"] & DefaultSchema["Views"])
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
        DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? (DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"] &
      DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Views"])[TableName] extends {
      Row: infer R
    }
    ? R
    : never
  : DefaultSchemaTableNameOrOptions extends keyof (DefaultSchema["Tables"] &
        DefaultSchema["Views"])
    ? (DefaultSchema["Tables"] &
        DefaultSchema["Views"])[DefaultSchemaTableNameOrOptions] extends {
        Row: infer R
      }
      ? R
      : never
    : never

export type TablesInsert<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Insert: infer I
    }
    ? I
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Insert: infer I
      }
      ? I
      : never
    : never

export type TablesUpdate<
  DefaultSchemaTableNameOrOptions extends
    | keyof DefaultSchema["Tables"]
    | { schema: keyof DatabaseWithoutInternals },
  TableName extends DefaultSchemaTableNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"]
    : never = never,
> = DefaultSchemaTableNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaTableNameOrOptions["schema"]]["Tables"][TableName] extends {
      Update: infer U
    }
    ? U
    : never
  : DefaultSchemaTableNameOrOptions extends keyof DefaultSchema["Tables"]
    ? DefaultSchema["Tables"][DefaultSchemaTableNameOrOptions] extends {
        Update: infer U
      }
      ? U
      : never
    : never

export type Enums<
  DefaultSchemaEnumNameOrOptions extends
    | keyof DefaultSchema["Enums"]
    | { schema: keyof DatabaseWithoutInternals },
  EnumName extends DefaultSchemaEnumNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"]
    : never = never,
> = DefaultSchemaEnumNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[DefaultSchemaEnumNameOrOptions["schema"]]["Enums"][EnumName]
  : DefaultSchemaEnumNameOrOptions extends keyof DefaultSchema["Enums"]
    ? DefaultSchema["Enums"][DefaultSchemaEnumNameOrOptions]
    : never

export type CompositeTypes<
  PublicCompositeTypeNameOrOptions extends
    | keyof DefaultSchema["CompositeTypes"]
    | { schema: keyof DatabaseWithoutInternals },
  CompositeTypeName extends PublicCompositeTypeNameOrOptions extends {
    schema: keyof DatabaseWithoutInternals
  }
    ? keyof DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"]
    : never = never,
> = PublicCompositeTypeNameOrOptions extends {
  schema: keyof DatabaseWithoutInternals
}
  ? DatabaseWithoutInternals[PublicCompositeTypeNameOrOptions["schema"]]["CompositeTypes"][CompositeTypeName]
  : PublicCompositeTypeNameOrOptions extends keyof DefaultSchema["CompositeTypes"]
    ? DefaultSchema["CompositeTypes"][PublicCompositeTypeNameOrOptions]
    : never

export const Constants = {
  public: {
    Enums: {},
  },
} as const

// Type aliases for easier usage
export type QuoteRecord = Tables<'quotes'>
export type QuoteItemRecord = Tables<'quote_items'>
export type TemplateRecord = Tables<'templates'>
export type ProductCatalogRecord = Tables<'products_catalog'>
export type QuoteHistoryRecord = Tables<'quote_history'>
export type NotificationRecord = Tables<'notifications'>
export type InvoiceRecord = Tables<'invoices'>
export type QuoteWithItems = Tables<'quotes_with_items'>
export type DashboardStats = Tables<'dashboard_stats'>

// Additional types
export type QuoteItemTemplate = {
  category: string
  subcategory?: string
  description: string
  quantity: number
  unit: string
  unit_price: number
  material_cost: number
  labor_cost: number
  complexity_factor: number
  is_optional: boolean
  supplier?: string
  notes?: string
}

export type AiGeneratedData = {
  items: QuoteItemTemplate[]
  summary: string
  aiEstimateTotal: number
  aiConfidence: number
  usedProvider: string
}

// Additional types for the application
export type Category =
  | "électricité"
  | "plomberie"
  | "maçonnerie"
  | "peinture"
  | "menuiserie"
  | "isolation"
  | "démolition"
  | "autre"

export type Unit = "m2" | "ml" | "u" | "h"

export type ClientInput = {
  typeRenovation: Category
  description: string
  surfaceM2?: number
  urgence: "basse" | "normale" | "haute"
  files?: File[]
}

export type LineItem = {
  id: string
  label: string
  category: Category
  quantity: number
  unit: Unit
  unitPrice: number
  total: number
  editable: boolean
}

export type Quote = {
  id: string
  currency: string
  items: LineItem[]
  totals: {
    subtotal: number
    discount: number
    margin: number
    vatRate: number
    vatAmount: number
    total: number
  }
}

export type Status = "en_attente" | "en_analyse" | "en_cours" | "validé" | "envoyé" | "accepté" | "refusé"

export type RequestRecord = {
  id: string
  createdAt: string
  updatedAt: string
  status: Status
  priority: number
  client: {
    name?: string
    email?: string
    phone?: string
  }
  input: ClientInput & {
    localisation?: string
    budgetIndicatif?: number
  }
  previews: {
    images: { url: string; name: string }[]
    plans: { url: string; name: string }[]
  }
  aiSummary: string
  quote: Quote
}
