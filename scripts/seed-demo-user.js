import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY

if (!supabaseUrl || !supabaseServiceKey) {
  console.error("Missing Supabase environment variables")
  process.exit(1)
}

// Create Supabase client with service role key for admin operations
const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false,
  },
})

async function seedDemoUser() {
  try {
    console.log("Creating demo user...")

    // Create the demo user
    const { data: authData, error: authError } = await supabase.auth.admin.createUser({
      email: "<EMAIL>",
      password: "demo123",
      email_confirm: true, // Skip email confirmation for demo user
      user_metadata: {
        phone: "+33 1 23 45 67 89",
      },
    })

    if (authError) {
      console.error("Error creating demo user:", authError.message)
      return
    }

    console.log("Demo user created successfully:", authData.user.email)

    // Create profile for the demo user
    const { error: profileError } = await supabase.from("profiles").insert({
      id: authData.user.id,
      name: "Utilisateur Démo",
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString(),
    })

    if (profileError) {
      console.error("Error creating demo user profile:", profileError.message)
      return
    }

    console.log("Demo user profile created successfully")
    console.log("Demo user credentials:")
    console.log("Email: <EMAIL>")
    console.log("Password: demo123")
  } catch (error) {
    console.error("Unexpected error:", error)
  }
}

// Run the seed function
seedDemoUser()
