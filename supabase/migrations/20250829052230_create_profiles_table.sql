-- Migration: Create profiles table
-- This table stores user profile information for both clients and professionals

CREATE TABLE IF NOT EXISTS public.profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT,
    name TEXT,
    role TEXT DEFAULT 'client',
    company_name TEXT,
    address TEXT,
    postal_code TEXT,
    city TEXT,
    siret TEXT,
    created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL
);

-- Enable RLS
ALTER TABLE public.profiles ENABLE ROW LEVEL SECURITY;

-- Create RLS policies (only if they don't exist)
DO $$
BEGIN
    -- Allow individual read access
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'profiles' AND policyname = 'Allow individual read access'
    ) THEN
        CREATE POLICY "Allow individual read access"
        ON public.profiles
        FOR SELECT
        USING (auth.uid() = id);
    END IF;

    -- Allow individual update access
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'profiles' AND policyname = 'Allow individual update access'
    ) THEN
        CREATE POLICY "Allow individual update access"
        ON public.profiles
        FOR UPDATE
        USING (auth.uid() = id)
        WITH CHECK (auth.uid() = id);
    END IF;

    -- Allow individual insert access
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'profiles' AND policyname = 'Allow individual insert access'
    ) THEN
        CREATE POLICY "Allow individual insert access"
        ON public.profiles
        FOR INSERT
        WITH CHECK (auth.uid() = id);
    END IF;

    -- Allow individual delete access
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'profiles' AND policyname = 'Allow individual delete access'
    ) THEN
        CREATE POLICY "Allow individual delete access"
        ON public.profiles
        FOR DELETE
        USING (auth.uid() = id);
    END IF;

    -- Allow admin full access
    IF NOT EXISTS (
        SELECT 1 FROM pg_policies
        WHERE tablename = 'profiles' AND policyname = 'Allow admin full access'
    ) THEN
        CREATE POLICY "Allow admin full access"
        ON public.profiles
        FOR ALL
        USING ((auth.jwt()::json -> 'app_metadata' ->> 'role') = 'admin');
    END IF;
END $$;

-- Create trigger for updated_at (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'update_profiles_updated_at' AND tgrelid = 'public.profiles'::regclass
    ) THEN
        CREATE TRIGGER update_profiles_updated_at
            BEFORE UPDATE ON public.profiles
            FOR EACH ROW
            EXECUTE FUNCTION update_updated_at_column();
    END IF;
END $$;

-- Create function to handle new user registration
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
LANGUAGE plpgsql
SECURITY DEFINER
SET search_path = public
AS $$
BEGIN
    INSERT INTO public.profiles (id, email, name, role)
    VALUES (
        new.id,
        new.email,
        coalesce(new.raw_user_meta_data ->> 'name', split_part(new.email, '@', 1)),
        coalesce(new.raw_user_meta_data ->> 'role', 'client')
    )
    ON CONFLICT (id) DO NOTHING;

    RETURN new;
END;
$$;

-- Create trigger to automatically create profile on user signup (only if it doesn't exist)
DO $$
BEGIN
    IF NOT EXISTS (
        SELECT 1 FROM pg_trigger
        WHERE tgname = 'on_auth_user_created' AND tgrelid = 'auth.users'::regclass
    ) THEN
        CREATE TRIGGER on_auth_user_created
            AFTER INSERT ON auth.users
            FOR EACH ROW
            EXECUTE FUNCTION public.handle_new_user();
    END IF;
END $$;
