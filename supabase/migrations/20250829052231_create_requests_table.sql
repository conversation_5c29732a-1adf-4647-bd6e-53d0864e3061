-- Migration: Create requests table
-- This table stores client renovation requests

CREATE TABLE IF NOT EXISTS public.requests (
    id TEXT PRIMARY KEY,
    created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()) NOT NULL,
    status TEXT DEFAULT 'en_attente' NOT NULL,
    priority INTEGER DEFAULT 2 NOT NULL,
    client_id UUID REFERENCES public.profiles(id),
    client_name TEXT,
    client_email TEXT,
    input JSONB NOT NULL,
    previews JSONB,
    ai_summary TEXT,
    quote JSONB,
    invoice_no TEXT,
    assigned_to UUID REFERENCES public.profiles(id),
    search_vector TSVECTOR GENERATED ALWAYS AS (
        to_tsvector('french'::regconfig,
            COALESCE(ai_summary, '') || ' ' ||
            COALESCE(client_name, '') || ' ' ||
            COALESCE(client_email, '')
        )
    ) STORED,
    CONSTRAINT requests_status_check CHECK (
        status = ANY (ARRAY[
            'en_attente'::text,
            'en_analyse'::text,
            'en_cours'::text,
            'validé'::text,
            'envoyé'::text,
            'accepté'::text,
            'refusé'::text
        ])
    )
);

-- Add comment for assigned_to column
COMMENT ON COLUMN public.requests.assigned_to IS 'ID of the pro user this request is assigned to';

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_requests_status ON public.requests(status);
CREATE INDEX IF NOT EXISTS idx_requests_created_at ON public.requests(created_at);
CREATE INDEX IF NOT EXISTS idx_requests_client_id ON public.requests(client_id);
CREATE INDEX IF NOT EXISTS idx_requests_assigned_to ON public.requests(assigned_to);
CREATE INDEX IF NOT EXISTS idx_requests_search_vector ON public.requests USING gin(search_vector);

-- Enable RLS
ALTER TABLE public.requests ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view their own requests"
ON public.requests
FOR SELECT
USING (
    client_id = auth.uid()
    OR assigned_to = auth.uid()
    OR EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
    )
);

CREATE POLICY "Pro users can view assigned requests"
ON public.requests
FOR SELECT
USING (
    assigned_to = auth.uid()
    OR EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid()
        AND profiles.role IN ('pro', 'admin')
    )
);

CREATE POLICY "Users can insert their own requests"
ON public.requests
FOR INSERT
WITH CHECK (client_id = auth.uid());

CREATE POLICY "Pro users can update assigned requests"
ON public.requests
FOR UPDATE
USING (
    assigned_to = auth.uid()
    OR client_id = auth.uid()
    OR EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
    )
)
WITH CHECK (
    assigned_to = auth.uid()
    OR client_id = auth.uid()
    OR EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
    )
);

-- Create trigger for updated_at
CREATE TRIGGER update_requests_updated_at
    BEFORE UPDATE ON public.requests
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();
