-- Migration: Create quotes table
-- This table stores renovation quotes linked to requests

CREATE TABLE IF NOT EXISTS public.quotes (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    request_id TEXT NOT NULL REFERENCES public.requests(id) ON DELETE CASCADE,
    version INTEGER DEFAULT 1,
    status TEXT DEFAULT 'brouillon',
    total_ht NUMERIC(12,2) DEFAULT 0,
    total_ttc NUMERIC(12,2) DEFAULT 0,
    tva_rate NUMERIC(4,2) DEFAULT 20.00,
    margin_rate NUMERIC(4,2) DEFAULT 15.00,
    created_by UUID REFERENCES public.profiles(id),
    modified_by UUID REFERENCES public.profiles(id),
    internal_comments TEXT,
    template_id UUID,
    ai_generated_data JSONB,
    custom_conditions TEXT,
    validity_days INTEGER DEFAULT 30,
    created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()),
    updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()),
    CONSTRAINT quotes_status_check CHECK (
        status = ANY (ARRAY[
            'brouillon'::text,
            'en_revision'::text,
            'validé'::text,
            'envoyé'::text,
            'accepté'::text,
            'refusé'::text
        ])
    )
);

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_quotes_request_id ON public.quotes(request_id);
CREATE INDEX IF NOT EXISTS idx_quotes_status ON public.quotes(status);
CREATE INDEX IF NOT EXISTS idx_quotes_created_by ON public.quotes(created_by);
CREATE INDEX IF NOT EXISTS idx_quotes_created_at ON public.quotes(created_at);

-- Enable RLS
ALTER TABLE public.quotes ENABLE ROW LEVEL SECURITY;

-- Create RLS policies
CREATE POLICY "Users can view quotes for their requests"
ON public.quotes
FOR SELECT
USING (
    EXISTS (
        SELECT 1 FROM public.requests r
        WHERE r.id = quotes.request_id
        AND (r.client_id = auth.uid() OR r.assigned_to = auth.uid())
    )
    OR created_by = auth.uid()
    OR EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
    )
);

CREATE POLICY "Pro users can create quotes"
ON public.quotes
FOR INSERT
WITH CHECK (
    created_by = auth.uid()
    AND EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid()
        AND profiles.role IN ('pro', 'admin')
    )
);

CREATE POLICY "Users can update their own quotes"
ON public.quotes
FOR UPDATE
USING (
    created_by = auth.uid()
    OR EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
    )
)
WITH CHECK (
    created_by = auth.uid()
    OR EXISTS (
        SELECT 1 FROM public.profiles
        WHERE profiles.id = auth.uid()
        AND profiles.role = 'admin'
    )
);

-- Create trigger for updated_at
CREATE TRIGGER update_quotes_updated_at
    BEFORE UPDATE ON public.quotes
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- Function to update modified_by and version on updates
CREATE OR REPLACE FUNCTION public.update_quote_metadata()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    -- Update modified_by and version on updates
    IF TG_OP = 'UPDATE' THEN
        NEW.modified_by := auth.uid();
        NEW.version := OLD.version + 1;
    END IF;

    RETURN NEW;
END;
$$;

-- Create trigger for quote metadata updates
CREATE TRIGGER update_quote_metadata_trigger
    BEFORE UPDATE ON public.quotes
    FOR EACH ROW
    EXECUTE FUNCTION public.update_quote_metadata();
