-- Table des postes de devis pour modification granulaire
CREATE TABLE quote_items (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  quote_id UUID NOT NULL REFERENCES quotes(id) ON DELETE CASCADE,
  category TEXT NOT NULL, -- électric<PERSON>, plomberie, maçonnerie, peinture, etc.
  subcategory TEXT, -- Plus de granularité si besoin
  description TEXT NOT NULL,
  quantity DECIMAL(10,3) DEFAULT 1, -- Permet les décimales pour mesures précises
  unit TEXT DEFAULT 'u', -- m², ml, u, kg, h, etc.
  unit_price DECIMAL(10,2) DEFAULT 0,
  material_cost DECIMAL(10,2) DEFAULT 0,
  labor_cost DECIMAL(10,2) DEFAULT 0,
  complexity_factor DECIMAL(3,2) DEFAULT 1.00, -- Facteur de difficulté
  total_price DECIMAL(12,2) GENERATED ALWAYS AS (
    COALESCE(quantity, 0) * COALESCE(unit_price, 0) * COALESCE(complexity_factor, 1)
  ) STORED,
  order_index INTEGER DEFAULT 0, -- Pour l'ordre d'affichage
  is_optional BOOLEAN DEFAULT false, -- Poste optionnel
  supplier TEXT, -- Fournisseur recommandé
  notes TEXT, -- Notes techniques
  ai_suggested BOOLEAN DEFAULT false, -- Indique si c'est une suggestion IA
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now())
);

-- Index pour performance
CREATE INDEX idx_quote_items_quote_id ON quote_items(quote_id);
CREATE INDEX idx_quote_items_category ON quote_items(category);
CREATE INDEX idx_quote_items_order ON quote_items(quote_id, order_index);

-- RLS
ALTER TABLE quote_items ENABLE ROW LEVEL SECURITY;

-- Trigger pour updated_at
CREATE TRIGGER update_quote_items_updated_at BEFORE UPDATE ON quote_items
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Trigger pour recalculer les totaux du devis quand un item change
CREATE OR REPLACE FUNCTION update_quote_totals()
RETURNS TRIGGER AS $$
BEGIN
  -- Recalcule les totaux du devis parent
  UPDATE quotes SET
    total_ht = (
      SELECT COALESCE(SUM(total_price), 0) 
      FROM quote_items 
      WHERE quote_id = COALESCE(NEW.quote_id, OLD.quote_id)
    ),
    updated_at = timezone('utc'::text, now())
  WHERE id = COALESCE(NEW.quote_id, OLD.quote_id);
  
  -- Recalcule le TTC
  UPDATE quotes SET 
    total_ttc = total_ht * (1 + tva_rate / 100)
  WHERE id = COALESCE(NEW.quote_id, OLD.quote_id);
  
  RETURN COALESCE(NEW, OLD);
END;
$$ language 'plpgsql';

CREATE TRIGGER update_quote_totals_on_item_change
    AFTER INSERT OR UPDATE OR DELETE ON quote_items
    FOR EACH ROW EXECUTE FUNCTION update_quote_totals();;
