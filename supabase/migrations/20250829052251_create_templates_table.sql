-- Table des templates pour bibliothèque personnelle du professionnel
CREATE TABLE templates (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  name TEXT NOT NULL,
  description TEXT,
  category TEXT NOT NULL, -- renovation_complete, salle_bain, cuisine, etc.
  subcategory TEXT,
  items JSONB NOT NULL DEFAULT '[]', -- Structure des postes du template
  default_margin NUMERIC(5,2) NOT NULL DEFAULT 15.00,
  estimated_duration_hours INTEGER, -- Durée estimée en heures
  complexity_level INTEGER CHECK (complexity_level IN (1,2,3,4,5)) DEFAULT 2, -- 1=simple, 5=très complexe
  created_by UUID NOT NULL REFERENCES profiles(id),
  is_public BOOLEAN DEFAULT false, -- Template partageable ou privé
  usage_count INTEGER DEFAULT 0, -- Nombre d'utilisations
  tags TEXT[], -- Tags pour recherche
  notes TEXT, -- Notes d'utilisation
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()),
  CONSTRAINT templates_default_margin_range CHECK (default_margin BETWEEN 0 AND 100)
);

-- Index pour performance et recherche
CREATE INDEX idx_templates_created_by ON templates(created_by);
CREATE INDEX idx_templates_category ON templates(category);
CREATE INDEX idx_templates_public ON templates(is_public) WHERE is_public = true;
CREATE INDEX idx_templates_tags ON templates USING GIN(tags);
CREATE INDEX idx_templates_search ON templates USING GIN(to_tsvector('french', name || ' ' || COALESCE(description, '')));

-- RLS
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;

-- Trigger pour updated_at
CREATE TRIGGER update_templates_updated_at BEFORE UPDATE ON templates
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Fonction pour incrémenter usage_count
CREATE OR REPLACE FUNCTION increment_template_usage(template_uuid UUID)
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  SET LOCAL search_path = pg_catalog, public;
  UPDATE templates
  SET usage_count = usage_count + 1,
      updated_at = timezone('utc'::text, now())
  WHERE id = template_uuid;
END;
$$;
