-- Historique des modifications pour traçabilité complète
CREATE TABLE quote_history (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  quote_id UUID NOT NULL REFERENCES quotes(id) ON DELETE CASCADE,
  version INTEGER NOT NULL,
  action TEXT NOT NULL CHECK (action IN ('created', 'updated', 'item_added', 'item_removed', 'item_modified', 'sent', 'accepted', 'rejected')),
  changes JSONB DEFAULT '{}', -- Détail des modifications JSON
  previous_values JSONB DEFAULT '{}', -- Valeurs avant modification
  new_values JSONB DEFAULT '{}', -- Nouvelles valeurs
  modified_by UUID NOT NULL REFERENCES profiles(id),
  comment TEXT, -- Commentaire de modification
  ip_address INET, -- Adresse IP pour audit
  user_agent TEXT, -- Navigateur pour audit
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now())
);

-- Index pour performance
CREATE INDEX idx_quote_history_quote_id ON quote_history(quote_id);
CREATE INDEX idx_quote_history_modified_by ON quote_history(modified_by);
CREATE INDEX idx_quote_history_created_at ON quote_history(created_at DESC);
CREATE INDEX idx_quote_history_action ON quote_history(action);

-- RLS
ALTER TABLE quote_history ENABLE ROW LEVEL SECURITY;

-- Fonction pour enregistrer automatiquement les changements
CREATE OR REPLACE FUNCTION log_quote_changes()
RETURNS TRIGGER
SECURITY DEFINER
SET search_path = public, pg_temp
AS $$
DECLARE
  changes_data JSONB := '{}';
  action_type TEXT;
BEGIN
  -- Détermine le type d'action
  IF TG_OP = 'INSERT' THEN
    action_type := 'created';
    changes_data := to_jsonb(NEW);
  ELSIF TG_OP = 'UPDATE' THEN
    action_type := 'updated';
    changes_data := jsonb_build_object(
      'changed_fields', (
        SELECT COALESCE(jsonb_object_agg(key, value), '{}'::jsonb)
        FROM jsonb_each(to_jsonb(NEW))
        WHERE value IS DISTINCT FROM (to_jsonb(OLD) -> key)
      )
    );
  END IF;

  -- Insère dans l'historique
  INSERT INTO quote_history (
    quote_id,
    version,
    action,
    changes,
    previous_values,
    new_values,
    modified_by
  ) VALUES (
    NEW.id,
    NEW.version,
    action_type,
    changes_data,
    CASE WHEN TG_OP = 'UPDATE' THEN to_jsonb(OLD) ELSE '{}' END,
    to_jsonb(NEW),
    COALESCE(auth.uid(), NEW.modified_by)
  );

  RETURN NEW;
END;
$$ language 'plpgsql';
-- Trigger pour historique automatique des devis
CREATE TRIGGER log_quote_changes_trigger
    AFTER INSERT OR UPDATE ON quotes
    FOR EACH ROW EXECUTE FUNCTION log_quote_changes();;
