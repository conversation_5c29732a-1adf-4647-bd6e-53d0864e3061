-- Table notifications pour relances automatiques et suivi
CREATE TABLE notifications (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  request_id TEXT REFERENCES requests(id) ON DELETE CASCADE,
  quote_id UUID REFERENCES quotes(id) ON DELETE CASCADE,
  type TEXT NOT NULL CHECK (type IN ('relance', 'lecture', 'acceptation', 'refus', 'expiration', 'modification')),
  status TEXT NOT NULL CHECK (status IN ('pending', 'sent', 'delivered', 'read', 'clicked', 'failed')) DEFAULT 'pending',
  recipient_email TEXT NOT NULL,
  recipient_name TEXT,
  subject TEXT,
  message TEXT,
  scheduled_at TIMESTAMPTZ,
  sent_at TIMESTAMPTZ,
  delivered_at TIMESTAMPTZ,
  read_at TIMESTAMPTZ,
  clicked_at TIMESTAMPTZ,
  failed_reason TEXT,
  retry_count INTEGER DEFAULT 0 CHECK (retry_count >= 0),
  max_retries INTEGER DEFAULT 3 CHECK (max_retries >= 0),
  metadata JSONB DEFAULT '{}', -- Données supplémentaires
  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now())
);

-- Index pour performance
CREATE INDEX idx_notifications_request_id ON notifications(request_id);
CREATE INDEX idx_notifications_quote_id ON notifications(quote_id);
CREATE INDEX idx_notifications_type ON notifications(type);
CREATE INDEX idx_notifications_status ON notifications(status);
CREATE INDEX idx_notifications_scheduled ON notifications(scheduled_at) WHERE status = 'pending';
CREATE INDEX idx_notifications_recipient ON notifications(recipient_email);

-- RLS
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;

-- Trigger pour updated_at
CREATE TRIGGER update_notifications_updated_at BEFORE UPDATE ON notifications
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

-- Fonction pour programmer une relance automatique
CREATE OR REPLACE FUNCTION schedule_quote_reminder(
  quote_uuid UUID,
  days_delay INTEGER DEFAULT 7,
  reminder_type TEXT DEFAULT 'relance'
)
RETURNS UUID AS $$
DECLARE
  notification_id UUID;
  quote_rec RECORD;
BEGIN
  -- Validate reminder_type
  IF reminder_type NOT IN ('relance', 'expiration') THEN
    RAISE EXCEPTION 'Invalid reminder_type: %. Allowed values are relance, expiration', reminder_type;
  END IF;

  -- Récupère les infos du devis et de la demande
  SELECT q.*, r.client_email, r.client_name
  INTO quote_rec
  FROM quotes q
  JOIN requests r ON q.request_id = r.id
  WHERE q.id = quote_uuid;
  IF NOT FOUND THEN
    RAISE EXCEPTION 'schedule_quote_reminder: quote % not found', quote_uuid
      USING ERRCODE = 'NO_DATA_FOUND';
  END IF;
  -- Crée la notification
  INSERT INTO notifications (
    request_id,
    quote_id,
    type,
    recipient_email,
    recipient_name,
    subject,
    scheduled_at
  ) VALUES (
    quote_rec.request_id,
    quote_uuid,
    reminder_type,
    quote_rec.client_email,
    quote_rec.client_name,
    CASE reminder_type
      WHEN 'relance' THEN 'Relance - Votre devis de rénovation'
      WHEN 'expiration' THEN 'Votre devis expire bientôt'
      ELSE 'Notification devis'
    END,
    timezone('utc'::text, now()) + INTERVAL '1 day' * days_delay
  ) RETURNING id INTO notification_id;
  
  RETURN notification_id;
END;
$$ language 'plpgsql';;
