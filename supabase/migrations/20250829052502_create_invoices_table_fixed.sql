-- Table factures pour conversion devis acceptés
CREATE TABLE invoices (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  invoice_number TEXT NOT NULL UNIQUE, -- Numérotation automatique
  quote_id UUID NOT NULL REFERENCES quotes(id),
  request_id TEXT NOT NULL REFERENCES requests(id),
  status TEXT CHECK (status IN ('brouillon', 'envoyée', 'payée', 'annulée')) DEFAULT 'brouillon',

  -- Informations client (dupliquées pour archivage)
  client_name TEXT NOT NULL,
  client_email TEXT NOT NULL,
  client_address TEXT,
  client_siret TEXT,

  -- Montants
  total_ht DECIMAL(12,2) NOT NULL,
  total_ttc DECIMAL(12,2) NOT NULL,
  tva_amount DECIMAL(12,2) NOT NULL,
  tva_rate DECIMAL(4,2) NOT NULL,

  -- Contraintes d'intégrité
  CONSTRAINT invoices_totals_non_negative CHECK (
    total_ht >= 0 AND total_ttc >= 0 AND tva_amount >= 0
  ),
  CONSTRAINT invoices_tva_rate_range CHECK (
    tva_rate >= 0 AND tva_rate <= 100
  ),
  CONSTRAINT invoices_total_consistency CHECK (
    total_ttc >= total_ht AND total_ttc = total_ht + tva_amount
  ),

  -- Échéances et paiement
  due_date DATE,
  payment_terms INTEGER DEFAULT 30, -- Jours
  payment_method TEXT,
  payment_date DATE,
  payment_reference TEXT,

  -- Archivage des items (snapshot du devis au moment de conversion)
  items JSONB NOT NULL DEFAULT '[]',

  -- Données comptables
  accounting_exported BOOLEAN DEFAULT false,
  accounting_export_date TIMESTAMPTZ,
  fiscal_year INTEGER, -- Sera rempli par trigger

  -- Suivi
  sent_at TIMESTAMPTZ,
  paid_at TIMESTAMPTZ,
  created_by UUID NOT NULL REFERENCES profiles(id),
  notes TEXT,

  created_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now()),
  updated_at TIMESTAMPTZ DEFAULT timezone('utc'::text, now())
);

-- Trigger pour remplir fiscal_year automatiquement
CREATE OR REPLACE FUNCTION set_fiscal_year()
RETURNS TRIGGER AS $$
BEGIN
  NEW.fiscal_year := EXTRACT(YEAR FROM NEW.created_at);
  RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER set_invoice_fiscal_year BEFORE INSERT ON invoices
    FOR EACH ROW EXECUTE FUNCTION set_fiscal_year();

-- Index pour performance et recherche
CREATE INDEX idx_invoices_invoice_number ON invoices(invoice_number);
CREATE INDEX idx_invoices_quote_id ON invoices(quote_id);
CREATE INDEX idx_invoices_request_id ON invoices(request_id);
CREATE INDEX idx_invoices_status ON invoices(status);
CREATE INDEX idx_invoices_client_email ON invoices(client_email);
CREATE INDEX idx_invoices_due_date ON invoices(due_date);
CREATE INDEX idx_invoices_fiscal_year ON invoices(fiscal_year);
CREATE INDEX idx_invoices_created_at ON invoices(created_at DESC);

-- RLS
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Trigger pour updated_at
CREATE TRIGGER update_invoices_updated_at BEFORE UPDATE ON invoices
    FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();;
