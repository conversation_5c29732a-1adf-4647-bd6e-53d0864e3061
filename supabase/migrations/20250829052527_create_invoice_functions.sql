-- Table pour les compteurs de factures par année
CREATE TABLE invoice_counters (
  year INTEGER PRIMARY KEY,
  counter INTEGER NOT NULL DEFAULT 0
);

-- Fonction pour générer le numéro de facture automatique
CREATE OR REPLACE FUNCTION generate_invoice_number()
RETURNS TEXT AS $$
DECLARE
  current_year INTEGER := EXTRACT(YEAR FROM NOW());
  next_number INTEGER;
  invoice_number TEXT;
BEGIN
  -- Obtient et incrémente atomiquement le compteur pour l'année en cours
  INSERT INTO invoice_counters (year)
  VALUES (current_year)
  ON CONFLICT (year) DO UPDATE SET
    counter = invoice_counters.counter + 1
  RETURNING counter INTO next_number;

  -- Formate le numéro
  invoice_number := 'F' || current_year || '-' || LPAD(next_number::TEXT, 4, '0');

  RETURN invoice_number;
END;
$$ language 'plpgsql';

-- Fonction pour convertir un devis en facture
CREATE OR REPLACE FUNCTION convert_quote_to_invoice(quote_uuid UUID)
RETURNS UUID AS $$
DECLARE
  invoice_id UUID;
  quote_rec RECORD;
  request_rec RECORD;
BEGIN
  -- Récupère les données du devis
  SELECT * INTO quote_rec
    FROM quotes
   WHERE id = quote_uuid;
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Devis introuvable (%).', quote_uuid;
  END IF;

  SELECT * INTO request_rec
    FROM requests
   WHERE id = quote_rec.request_id;
  IF NOT FOUND THEN
    RAISE EXCEPTION 'Demande liée introuvable pour le devis (%).', quote_uuid;
  END IF;

  -- Vérifie que le devis est accepté
  IF quote_rec.status != 'accepté' THEN
    RAISE EXCEPTION 'Le devis doit être accepté pour être converti en facture';
  END IF;
  -- Crée la facture
  INSERT INTO invoices (
    invoice_number,
    quote_id,
    request_id,
    client_name,
    client_email,
    total_ht,
    total_ttc,
    tva_amount,
    tva_rate,
    due_date,
    items,
    created_by
  ) VALUES (
    generate_invoice_number(),
    quote_uuid,
    quote_rec.request_id,
    request_rec.client_name,
    request_rec.client_email,
    quote_rec.total_ht,
    quote_rec.total_ttc,
    quote_rec.total_ttc - quote_rec.total_ht,
    quote_rec.tva_rate,
    CURRENT_DATE + INTERVAL '30 days',
    COALESCE((
      SELECT jsonb_agg(
        jsonb_build_object(
          'description', description,
          'quantity', quantity,
          'unit', unit,
          'unit_price', unit_price,
          'total_price', total_price,
          'category', category
        ) ORDER BY order_index
      ) FROM quote_items WHERE quote_id = quote_uuid
    ), '[]'::jsonb),
    quote_rec.created_by
  ) RETURNING id INTO invoice_id;
  
  -- Met à jour la demande avec le numéro de facture
  UPDATE requests SET 
    invoice_no = (SELECT invoice_number FROM invoices WHERE id = invoice_id)
  WHERE id = quote_rec.request_id;
  
  RETURN invoice_id;
END;
$$ language 'plpgsql';;
