-- Ajouter la contrainte FK pour templates.template_id dans quotes
ALTER TABLE quotes
  ADD CONSTRAINT fk_quotes_template_id
  FOREIGN KEY (template_id)
  REFERENCES templates(id)
  ON DELETE SET NULL
  NOT VALID;

-- Optional cleanup: null out broken references before validating
UPDATE quotes q
SET template_id = NULL
WHERE template_id IS NOT NULL
  AND NOT EXISTS (SELECT 1 FROM templates t WHERE t.id = q.template_id);

ALTER TABLE quotes VALIDATE CONSTRAINT fk_quotes_template_id;

-- Performance: speed up ON DELETE SET NULL and joins
CREATE INDEX IF NOT EXISTS idx_quotes_template_id ON quotes(template_id);

-- <PERSON><PERSON><PERSON> des vues utiles pour l'interface professionnel

-- Vue complète des devis avec leurs items
CREATE OR REPLACE VIEW quotes_with_items AS
SELECT 
  q.*,
  COALESCE(
    json_agg(
      json_build_object(
        'id', qi.id,
        'category', qi.category,
        'subcategory', qi.subcategory,
        'description', qi.description,
        'quantity', qi.quantity,
        'unit', qi.unit,
        'unit_price', qi.unit_price,
        'material_cost', qi.material_cost,
        'labor_cost', qi.labor_cost,
        'complexity_factor', qi.complexity_factor,
        'total_price', qi.total_price,
        'order_index', qi.order_index,
        'is_optional', qi.is_optional,
        'supplier', qi.supplier,
        'notes', qi.notes,
        'ai_suggested', qi.ai_suggested
      ) ORDER BY qi.order_index
    ) FILTER (WHERE qi.id IS NOT NULL),
    '[]'::json
  ) as items,
  r.client_name,
  r.client_email,
  r.ai_summary,
  r.status as request_status
FROM quotes q
LEFT JOIN quote_items qi ON q.id = qi.quote_id
LEFT JOIN requests r ON q.request_id = r.id
GROUP BY q.id, r.client_name, r.client_email, r.ai_summary, r.status;

-- Vue tableau de bord pro
CREATE OR REPLACE VIEW dashboard_stats AS
SELECT 
  COUNT(DISTINCT r.id) FILTER (WHERE r.status = 'en_attente') as pending_requests,
  COUNT(DISTINCT r.id) FILTER (WHERE r.status = 'en_analyse') as analyzing_requests,
  COUNT(*) FILTER (WHERE q.status = 'brouillon') as draft_quotes,
  COUNT(*) FILTER (WHERE q.status = 'envoyé') as sent_quotes,
  COUNT(*) FILTER (WHERE q.status = 'accepté') as accepted_quotes,
  COALESCE(SUM(q.total_ttc) FILTER (WHERE q.status = 'accepté'), 0) as total_accepted_amount,
  COALESCE(AVG(q.total_ttc) FILTER (WHERE q.status = 'accepté'), 0) as avg_quote_amount
FROM requests r
LEFT JOIN quotes q ON r.id = q.request_id
WHERE r.created_at >= CURRENT_DATE - INTERVAL '30 days';

-- Index pour les vues
CREATE INDEX idx_requests_status_created_at ON requests(status, created_at);
CREATE INDEX idx_quotes_status_created_at ON quotes(status, created_at);;
