-- Politiques RLS pour sécuriser l'accès aux données existantes

-- Enable RLS on existing tables
ALTER TABLE quotes ENABLE ROW LEVEL SECURITY;
ALTER TABLE quote_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE quote_history ENABLE ROW LEVEL SECURITY;
ALTER TABLE notifications ENABLE ROW LEVEL SECURITY;
ALTER TABLE invoices ENABLE ROW LEVEL SECURITY;

-- Politique pour quotes : seuls les admins voient tous les devis, les pros voient seulement les leurs
CREATE POLICY quotes_pro_access ON quotes
FOR ALL
TO authenticated
USING (
  -- Admin: full access to all quotes
  EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
  -- Pro: access to own quotes only
  OR (
    EXISTS (
      SELECT 1 FROM profiles
      WHERE profiles.id = auth.uid()
      AND profiles.role = 'pro'
    )
    AND quotes.created_by = auth.uid()
  )
);

-- Politique pour quote_items : liée aux quotes
CREATE POLICY quote_items_pro_access ON quote_items
FOR ALL
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM quotes q
    WHERE q.id = quote_items.quote_id
      AND (
        q.created_by = auth.uid() OR
        EXISTS (
          SELECT 1
          FROM profiles p
          WHERE p.id = auth.uid()
            AND p.role = 'admin'
        )
      )
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM quotes q
    WHERE q.id = quote_items.quote_id
      AND (
        q.created_by = auth.uid() OR
        EXISTS (
          SELECT 1
          FROM profiles p
          WHERE p.id = auth.uid()
            AND p.role = 'admin'
        )
      )
  )
);

-- Politique pour templates : les pros voient leurs templates + les publics
CREATE POLICY templates_access ON templates
FOR SELECT
TO authenticated
USING (
  -- Own templates
  created_by = auth.uid()
  -- Public templates
  OR is_public = true
  -- Admin: all templates
  OR EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
);

CREATE POLICY templates_crud ON templates
FOR ALL
TO authenticated
USING (
  -- Own templates (pro/admin can create/update their own)
  created_by = auth.uid()
  -- Admin: full CRUD access
  OR EXISTS (
    SELECT 1 FROM profiles
    WHERE profiles.id = auth.uid()
    AND profiles.role = 'admin'
  )
)
WITH CHECK (
  -- Own templates (pro/admin can create/update their own)
  created_by = auth.uid()
  -- Admin: full CRUD access
  OR EXISTS (
    SELECT 1
    FROM profiles p
    WHERE p.id = auth.uid()
    AND p.role = 'admin'
  )
);

-- Politique pour quote_history : lecture seule pour les pros
CREATE POLICY quote_history_read ON quote_history
FOR SELECT
TO authenticated
USING (
  EXISTS (
    SELECT 1
    FROM quotes q
    WHERE q.id = quote_history.quote_id
      AND (
        q.created_by = auth.uid() OR
        EXISTS (
          SELECT 1
          FROM profiles p
          WHERE p.id = auth.uid()
            AND p.role = 'admin'
        )
      )
  )
);

-- Politique pour notifications : accès selon le contexte
CREATE POLICY notifications_access ON notifications
FOR ALL
TO authenticated
USING (
  (
    quote_id IS NULL
    AND EXISTS (SELECT 1 FROM profiles p WHERE p.id = auth.uid() AND p.role = 'admin')
  )
  OR EXISTS (
    SELECT 1
    FROM quotes q
    WHERE q.id = notifications.quote_id
      AND (
        q.created_by = auth.uid()
        OR EXISTS (SELECT 1 FROM profiles p WHERE p.id = auth.uid() AND p.role = 'admin')
      )
  )
)
WITH CHECK (
  EXISTS (
    SELECT 1
    FROM quotes q
    WHERE q.id = notifications.quote_id
      AND (
        q.created_by = auth.uid()
        OR EXISTS (SELECT 1 FROM profiles p WHERE p.id = auth.uid() AND p.role = 'admin')
      )
  )
);

-- Politique pour invoices : seuls les pros et admins
CREATE POLICY invoices_pro_access ON invoices
FOR ALL
TO authenticated
USING (
  created_by = auth.uid()
  OR EXISTS (
    SELECT 1
    FROM profiles p
    WHERE p.id = auth.uid()
      AND p.role = 'admin'
  )
)
WITH CHECK (
  created_by = auth.uid()
  OR EXISTS (
    SELECT 1
    FROM profiles p
    WHERE p.id = auth.uid()
      AND p.role = 'admin'
  )
);
