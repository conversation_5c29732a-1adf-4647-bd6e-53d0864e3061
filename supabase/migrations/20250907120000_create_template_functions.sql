CREATE OR REPLACE FUNCTION get_user_templates()
RETURNS SETOF public.templates AS $$
BEGIN
  RETURN QUERY
  SELECT *
  FROM public.templates
  WHERE created_by = auth.uid() OR is_public
  ORDER BY usage_count DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql;

CREATE OR REPLACE FUNCTION get_templates_by_category(p_category TEXT)
RETURNS SETOF public.templates AS $$
BEGIN
    RETURN QUERY
    SELECT *
    FROM public.templates
    WHERE category = p_category
    AND (created_by = auth.uid() OR is_public)
    ORDER BY usage_count DESC NULLS LAST;
END;
$$ LANGUAGE plpgsql STABLE SECURITY INVOKER;
