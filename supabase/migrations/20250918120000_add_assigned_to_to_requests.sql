-- Migration: Add assigned_to column to requests table
-- Created: 2025-09-18
-- Purpose: Allow assignment of requests to pro users

-- The assigned_to column is already created in the main requests table migration
-- This migration is kept for backwards compatibility but no operations are needed

-- Add comment for the existing assigned_to column
COMMENT ON COLUMN public.requests.assigned_to IS 'ID of the pro user this request is assigned to';

-- No operations needed as the column is already created in the table creation migration
